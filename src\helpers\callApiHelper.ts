import { HttpService } from '@nestjs/axios'
import { UnauthorizedException } from '@nestjs/common'
import { Request as IRequest } from 'express'
import { lastValueFrom } from 'rxjs'
import * as FormData from 'form-data';

class CallApiHelper {
  constructor(private httpService: HttpService) { }

  /** Hàm gọi http request */
  processCallApiHelper(req: IRequest, data: any, apiUrl: string): Promise<any> {
    //const user = JSON.stringify((req as any).user)
    const key_secret_micro = process.env.KEY_SECRET_MICRO
    const key_bl_admin_oms = process.env.KEY_BL_ADMIN_OMS
    const key_integration = process.env.KEY_BL_INTEGRATION_OMS
    //if (!user) throw new UnauthorizedException('Không có quyền truy cập! (code: DMS_AUTH_TOKEN_ERROR)')
    if (!key_secret_micro) throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_SECRET_MICRO_ERROR)')
    //if (!key_bl_admin_oms) throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_BL_ADMIN_OMS_ERROR)')

    return new Promise((resolve, reject) => {
      //const header = { headers: { origin: req.headers.origin, user, 'x-apikey': key_secret_micro } }
      const header = {
        headers: {
          origin: req.headers.origin,
          'x-apikey': key_secret_micro,
          'x-bl-authorization': `APIKey ${key_bl_admin_oms}`,
          'x-api-authorization': key_integration,
        }
      }
      const request = this.httpService.post(apiUrl, data, header)
      lastValueFrom(request)
        .then((res) => {
          resolve(res?.data)
        })
        .catch((err: any) => {
          reject(err?.response)
        })
    })
  }

  /** Hàm gọi http request */
  processPutCallApiHelper(req: IRequest, data: any, apiUrl: string): Promise<any> {
    //const user = JSON.stringify((req as any).user)
    const key_secret_micro = process.env.KEY_SECRET_MICRO
    const key_bl_admin_oms = process.env.KEY_BL_ADMIN_OMS
    // if (!user) throw new UnauthorizedException('Không có quyền truy cập! (code: DMS_AUTH_TOKEN_ERROR)')
    if (!key_secret_micro) throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_SECRET_MICRO_ERROR)')
    // if (!key_bl_admin_oms) throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_BL_ADMIN_OMS_ERROR)')

    return new Promise((resolve, reject) => {
      //const header = { headers: { origin: req.headers.origin, user, 'x-apikey': key_secret_micro } }
      const header = {
        headers: {
          origin: req.headers.origin,
          'x-bl-authorization': `APIKey ${key_bl_admin_oms}`,
          'x-apikey': key_secret_micro,
        }
      }
      const request = this.httpService.put(apiUrl, data, header)
      lastValueFrom(request)
        .then((res) => {
          resolve(res?.data)
        })
        .catch((err: any) => {
          reject(err?.response)
        })
    })
  }

  processCallGetApiHelper(req: IRequest, apiUrl: string): Promise<any> {
    //const user = JSON.stringify((req as any).user)
    const key_secret_micro = process.env.KEY_SECRET_MICRO
    const key_bl_admin_oms = process.env.KEY_BL_ADMIN_OMS
    //if (!user) throw new UnauthorizedException('Không có quyền truy cập! (code: AUTH_TOKEN_ERROR)')
    if (!key_secret_micro) throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_SECRET_MICRO_ERROR)')
    //if (!key_bl_admin_oms) throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_BL_ADMIN_OMS_ERROR)')

    return new Promise((resolve, reject) => {
      //const header = { headers: { origin: req.headers.origin, user, 'key-secret-micro': key_secret_micro } }
      const header = {
        headers: {
          origin: req.headers.origin,
          'x-bl-authorization': `APIKey ${key_bl_admin_oms}`,
          'key-secret-micro': key_secret_micro,
        }
      }
      const request = this.httpService.get(apiUrl, header)
      lastValueFrom(request)
        .then((res) => {
          resolve(res?.data)
        })
        .catch((err: any) => {
          reject(err?.response)
        })
    })
  }

  processDeleteCallApiHelper(req: IRequest, data: any, apiUrl: string): Promise<any> {
    //const user = JSON.stringify((req as any).user)
    const key_secret_micro = process.env.KEY_SECRET_MICRO
    const key_bl_admin_oms = process.env.KEY_BL_ADMIN_OMS
    //if (!user) throw new UnauthorizedException('Không có quyền truy cập! (code: DMS_AUTH_TOKEN_ERROR)')
    if (!key_secret_micro) throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_SECRET_MICRO_ERROR)')
    //if (!key_bl_admin_oms) throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_BL_ADMIN_OMS_ERROR)')

    return new Promise((resolve, reject) => {
      //const header = { headers: { origin: req.headers.origin, user, 'x-apikey': key_secret_micro } }
      const header = {
        headers: {
          origin: req.headers.origin,
          'x-bl-authorization': `APIKey ${key_bl_admin_oms}`,
          'x-apikey': key_secret_micro,
        }
      }
      const request = this.httpService.delete(apiUrl, header)
      lastValueFrom(request)
        .then((res) => {
          resolve(res?.data)
        })
        .catch((err: any) => {
          reject(err?.response)
        })
    })
  }

  // Gửi file service
  async processCallApiHelperWithFile(
    req: IRequest,
    file: Express.Multer.File,
    apiUrl: string,
  ) {
    const key_secret_micro = process.env.KEY_SECRET_MICRO;
    const key_bl_admin_oms = process.env.KEY_BL_ADMIN_OMS;

    if (!key_secret_micro) {
      throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_SECRET_MICRO_ERROR)');
    }

    const form = new FormData();

    form.append('file', Buffer.from(file.buffer), {
      filename: file.originalname,
      contentType: file.mimetype,
      knownLength: file.size,
    });

    const headers = {
      ...form.getHeaders(), // 👈 merge Content-Type: multipart/form-data
      origin: req.headers.origin,
      'x-bl-authorization': `APIKey ${key_bl_admin_oms}`,
      'x-apikey': key_secret_micro,
    };

    try {
      const response = await lastValueFrom(
        this.httpService.post(apiUrl, form, { headers }),
      );
      return response?.data;
    } catch (err: any) {
      throw err?.response || err;
    }
  }



  /** Geo API GET request (without createHttpClient) */
  async processGeoApiRequest(apiUrl: string): Promise<any> {
    const geoApiBaseUrl = 'https://provinces.open-api.vn'
    try {
      const url = geoApiBaseUrl + '/' + apiUrl
      const response = await lastValueFrom(
        this.httpService.get(url, {
          headers: { 'Content-Type': 'application/json', Accept: 'application/json' },
        }),
      )
      return response.data
    } catch (error) {
      return null
    }
  }
}

export const callApiHelper = new CallApiHelper(new HttpService())
