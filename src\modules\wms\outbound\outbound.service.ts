import { Injectable } from '@nestjs/common'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Equal, In, IsNull, Like, <PERSON><PERSON>han, Not, Raw } from 'typeorm'
import { Request as IRequest } from 'express'
import * as moment from 'moment'
import { v4 as uuidv4 } from 'uuid'

import {
  OutboundRepository,
  ItemDetailRepository,
  ItemPriceRepository,
  ItemRepository,
  WarehouseProductDetailRepository,
  WarehouseProductRepository,
  WarehouseRepository,
  UserRepository,
} from '../../../repositories'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ERROR_NOT_FOUND_DATA, NSWarehouse, enumData } from '../../../constants'
import { coreHelper } from '../../../helpers'
import { OutboundCreateComboDto, OutboundCreateDto, OutboundUpdateComboDto, OutboundUpdateDto, MinusEmployeeProductQuantityDto } from './dto'
import {
  OutboundDetailEntity,
  OutboundEntity,
  OutboundHistoryEntity,
  ItemDetailEntity,
  ItemEntity,
  ProductInventoryHistoryEntity,
  ItemPriceEntity,
  WarehouseProductDetailEntity,
  WarehouseProductEntity,
} from '../../../entities'
import { InboundService } from '../inbound/inbound.service'
import { CheckingRemainDto } from './dto/checkRemail.dto'
import { omsApiHelper } from '../../../helpers/omsApiHelper'

interface OutboundEntityExtend extends OutboundEntity {
  __outboundDetails__?: OutboundDetailEntity[]
  __histories__?: OutboundHistoryEntity[]
}
@Injectable()
export class OutboundService {
  constructor(
    private readonly repo: OutboundRepository,
    private readonly warehouseRepo: WarehouseRepository,
    private readonly productDetailRepo: ItemDetailRepository,
    private readonly productRepo: ItemRepository,
    private readonly warehouseProductRepo: WarehouseProductRepository,
    private readonly warehouseProductDetailRepo: WarehouseProductDetailRepository,
    private readonly inboundService: InboundService,
    private readonly productPriceRepo: ItemPriceRepository,
    private userRepo: UserRepository,
  ) {}

  /** Hàm tìm tất cả phiếu xuất kho */
  async find(data: any) {
    return await this.repo.find(data)
  }

  /** Hàm tìm thông tin chi tiết phiếu xuất kho */
  async findDetail(data: FilterOneDto, req: IRequest) {
    const entity: any = await this.repo.findOne({
      where: { id: data.id, isDeleted: false },
      relations: { outboundDetails: true, histories: true, warehouse: true },
    })

    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.lstOutboundDetail = entity.__outboundDetails__
    entity.lstHistory = entity.__histories__
    entity.warehouseName = entity.__warehouse__.name
    entity.statusName = enumData.OutboundStatus[entity.status]?.name
    entity.statusColor = enumData.OutboundStatus[entity.status]?.color
    entity.typeName = enumData.OutboundType[entity.type]?.name

    if (entity.employeeId) {
      // const employee = await authApiHelper.getEmployeeName(req, { id: entity.employeeId })
      // if (employee) entity.employeeName = employee.name
    }
    for (let od of entity.lstOutboundDetail) {
      od.__product__ = await this.productRepo.findOne({ where: { id: od.productId }, relations: { unit: true, details: true } })
      od.__productDetail__ = await this.productDetailRepo.findOne({ where: { id: od.productDetailId } })
    }

    entity.lstOutboundComboDetail = entity.lstOutboundNotComboDetail = []
    if (entity.type != enumData.OutboundType.COMBO.code)
      for (let od of entity.lstOutboundDetail) {
        od.productName = od.__product__.name
        od.productCode = od.__product__.code
        od.unitName = od.__product__?.__unit__?.name
        od.lstProductDetail = od.__product__?.__details__ || []
        if (!od.manufactureDate) od.manufactureDate = od?.__productDetail__?.manufactureDate ?? ''
        if (!od.lotNumber) od.lotNumber = od.__productDetail__?.lotNumber ?? ''
        for (let pd of od.lstProductDetail) pd.expiryDateFmt = moment(pd.expiryDate).format('DD/MM/YYYY')

        delete od.__productDetail__
      }
    else {
      entity.lstOutboundComboDetail = entity.lstOutboundDetail.filter((e) => e.isCombo == true)
      entity.lstOutboundNotComboDetail = entity.lstOutboundDetail.filter((e) => e.isCombo == false)

      for (let od of entity.lstOutboundComboDetail) {
        od.productName = od.__product__.name
        od.productCode = od.__product__.code
        od.expand = true
        od.lstOutboundDetail = []

        for (let odnc of entity.lstOutboundNotComboDetail) {
          odnc.productName = odnc.__product__.name
          odnc.productCode = odnc.__product__.code
          odnc.unitName = odnc.__product__?.__unit__?.name
          odnc.lstProductDetail = odnc.__product__?.__details__ || []

          for (let pd of odnc.lstProductDetail) pd.expiryDateFmt = moment(pd.expiryDate).format('DD/MM/YYYY')

          if (odnc.outboundDetailComboId == od.id) {
            od.lstOutboundDetail.push(odnc)
          }
        }
      }
    }

    delete entity.__outboundDetails__
    delete entity.__histories__
    delete entity.__warehouse__
    const memberCreate: any = (await omsApiHelper.getMemberByListId(req, [entity.createdBy])) || []
    const memberPrepare: any = (await omsApiHelper.getMemberByListId(req, [entity.preparedBy])) || []
    const memberApprove: any = (await omsApiHelper.getMemberByListId(req, [entity.approvedBy])) || []
    entity.createdByName = memberCreate[0]?.fullName
    entity.preparedByName = memberPrepare[0]?.fullName
    entity.approveByName = memberApprove[0]?.fullName
    return entity
  }

  /** Hàm lấy mã tự sinh để hiện thị lên frontEnd */
  async getCodeAutoGen(): Promise<{ code: string }> {
    // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: entity.map
    const curDate = moment(new Date()).format('DDMMYYYY')

    let genCode: string
    // Đếm số lượng phiếu xuất kho trong ngày
    let count = await this.repo.count({ where: { code: Like(`${curDate}_%`) }, select: { id: true } })
    if (!count) count = 0

    genCode = this.genCode(count)

    return { code: genCode }
  }

  /** Sinh mã tăng dần ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001 */
  genCode(count: number, plusString: string = ''): string {
    const curDate = moment(new Date()).format('DDMMYYYY')
    let numberString: string = '0001'
    let genCode: string
    const incrementedNumber = Number(numberString) + count
    const newLastPart = String(incrementedNumber).padStart(numberString.length, '0')
    genCode = `${curDate}_${newLastPart}`
    if (plusString.length > 0) genCode = `${plusString}_${curDate}_${newLastPart}`
    return genCode
  }

  /** Hàm phân trang phiếu xuất kho */
  async pagination(data: PaginationDto, req: IRequest, userlogin?: UserDto) {
    try {
      const whereCon: any = {}
      if (data.where.approvedBy) whereCon.approvedBy = data.where.approvedBy
      if (data.where.preparedBy) whereCon.preparedBy = data.where.preparedBy
      if (data.where.createdBy) whereCon.createdBy = data.where.createdBy
      if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
      if (data.where.orderCode) whereCon.orderCode = Like(`%${data.where.orderCode}%`)
      if (data.where.customerName) whereCon.customerName = Like(`%${data.where.customerName}%`)
      if (data.where.warehouseId) whereCon.warehouseId = data.where.warehouseId

      if (data.where.employeeId) whereCon.employeeId = data.where.employeeId
      if (data.where.type) whereCon.type = data.where.type
      if (data.where.createdAt) {
        const formattedDate = new Date(data.where.createdAt).toISOString().split('T')[0]
        whereCon.createdAt = Raw((alias) => `DATE(${alias}) = '${formattedDate}'`)
      }
      if (data.where.updatedAt) {
        const formattedDate = new Date(data.where.updatedAt).toISOString().split('T')[0]
        whereCon.updatedAt = Raw((alias) => `DATE(${alias}) = '${formattedDate}'`)
      }
      whereCon.outboundDetails = {}

      if (data.where.brandId) {
        whereCon.outboundDetails.product.brandId = Like(`%${data.where.brandId}%`)
      }

      let whereOr: any = {}
      if (data.where.status) {
        whereCon.status = In(data.where.status)
        whereOr = { ...whereCon }
      } else if (data.where.lstStatus) {
        const whereConLst: any = { ...whereCon }
        whereConLst.status = In(data.where.lstStatus)
        whereOr = { ...whereCon, ...whereConLst }
      } else {
        whereCon.status = 'New'
        whereOr = { ...whereCon }
      }

      const [lstOb, total]: any = await this.repo.findAndCount({
        where: whereOr,
        order: { createdAt: 'DESC' },
        skip: data.skip,
        take: data.take,
        relations: { warehouse: true },
      })
      if (lstOb.length == 0) return { data: lstOb, total }

      for (let e of lstOb) {
        e.warehouseName = e.__warehouse__?.name ?? ''
        e.typeName = enumData.OutboundType[e.type]?.name ?? ''
        e.statusName = enumData.OutboundStatus[e.status]?.name ?? ''
        e.statusColor = enumData.OutboundStatus[e.status]?.color ?? ''
        delete e.__warehouse__
      }

      // Người tạo phiếu
      const listMemCreateIds = lstOb.map((val) => val.createdBy)
      const memberCreate: any[] = (await omsApiHelper.getMemberByListId(req, listMemCreateIds)) || []

      // Người tạo phiếu
      const listMemPrepareIds = lstOb.map((val) => val.preparedBy)
      const memberPrepare: any[] = (await omsApiHelper.getMemberByListId(req, listMemPrepareIds)) || []

      // Người tạo phiếu
      const listMemApproveIds = lstOb.map((val) => val.approvedBy)
      const memberApprove: any[] = (await omsApiHelper.getMemberByListId(req, listMemApproveIds)) || []

      const mappingResult = lstOb.map((val) => {
        const memCreate: any = memberCreate.find((m: any) => m.id === val.createdBy)
        const memPrepare: any = memberPrepare.find((m: any) => m.id === val.preparedBy)
        const memApproved: any = memberApprove.find((m: any) => m.id === val.approvedBy)

        return {
          ...val,
          createdByName: memCreate?.fullName,
          preparedByName: memPrepare?.fullName,
          approvedByName: memApproved?.fullName,
        }
      })

      return { data: mappingResult, total }
    } catch (error) {
      throw new Error('Lỗi lấy danh sách PXK')
    }
  }

  /** Hàm phân trang phiếu xuất kho cho view lịch sử */
  async paginationForHistoryView(data: PaginationDto, req: IRequest) {
    try {
      const whereCon: any = {}
      if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
      if (data.where.orderCode) whereCon.orderCode = Like(`%${data.where.orderCode}%`)
      if (data.where.customerName) whereCon.customerName = Like(`%${data.where.customerName}%`)
      if (data.where.warehouseId) whereCon.warehouseId = data.where.warehouseId

      if (data.where.employeeId) whereCon.employeeId = data.where.employeeId
      whereCon.outboundDetails = {}
      whereCon.outboundDetails = {}
      if (data.where.productCode) {
        whereCon.outboundDetails.productCode = Like(`%${data.where.productCode}%`)
      }
      if (data.where.productName) {
        whereCon.outboundDetails.productName = Like(`%${data.where.productName}%`)
      }
      if (data.where.type) whereCon.type = data.where.type
      if (data.where.createdAt) {
        const formattedDate = new Date(data.where.createdAt).toISOString().split('T')[0]
        whereCon.createdAt = Raw((alias) => `DATE(${alias}) = '${formattedDate}'`)
      }
      if (data.where.approvedDate) {
        const formattedDate = new Date(data.where.approvedDate).toISOString().split('T')[0]
        whereCon.approvedDate = Raw((alias) => `DATE(${alias}) = '${formattedDate}'`)
      }

      if (data.where.shippedDate) {
        const formattedDate = new Date(data.where.shippedDate).toISOString().split('T')[0]
        whereCon.approvedDate = Raw((alias) => `DATE(${alias}) = '${formattedDate}'`)
      }
      // Tìm người xác nhận soạn hàng
      if (data.where.preparedByName) {
        // let lstPreparedUserId: string[] = await authApiHelper.getLstUserIdByName(req, { name: data.where.preparedByName })
        // if (lstPreparedUserId.length > 0) whereCon.preparedBy = In(lstPreparedUserId)
        // else return [[], 0]
      }
      // Tìm người xác nhận soạn hàng
      if (data.where.approvedByName) {
        // let lstApproveUserId: string[] = await authApiHelper.getLstUserIdByName(req, { name: data.where.approvedByName })
        // if (lstApproveUserId.length > 0) whereCon.approvedBy = In(lstApproveUserId)
        // else return [[], 0]
      }

      if (data.where.brandId) {
        whereCon.outboundDetails.product.brandId = Like(`%${data.where.brandId}%`)
      }

      // ...data.where?.warehouseType && { warehouse: { type: data.where.warehouseType } },
      if (data.where.warehouseType) {
        whereCon.warehouse = { type: data.where.warehouseType }
      }

      let whereOr: any[] = []
      if (data.where.status) {
        whereCon.status = In(data.where.status)
        whereOr = [{ ...whereCon }]
      } else {
        const whereConLst: any = { ...whereCon }
        whereConLst.status = In(data.where.lstStatus)
        whereOr = [{ ...whereCon }, { ...whereConLst }]
      }

      const [lstOutbound, total]: any = await this.repo.findAndCount({
        where: whereOr,
        order: { createdAt: 'DESC' },
        skip: data.skip,
        take: data.take,
        relations: { warehouse: true },
      })
      for (let e of lstOutbound) {
        e.warehouseName = e.__warehouse__?.name ?? ''
        e.typeName = enumData.OutboundType[e.type]?.name ?? ''
        e.statusName = enumData.OutboundStatus[e.status]?.name ?? ''
        e.statusColor = enumData.OutboundStatus[e.status]?.color ?? ''
        delete e.__warehouse__
      }
      if (lstOutbound.length == 0) return { data: [], total: 0 }
      const listMemCreateIds = lstOutbound.map((val) => val.createdBy)
      const memberCreate: any[] = (await omsApiHelper.getMemberByListId(req, listMemCreateIds)) || []

      // Người tạo phiếu
      const listMemPrepareIds = lstOutbound.map((val) => val.preparedBy)
      const memberPrepare: any[] = (await omsApiHelper.getMemberByListId(req, listMemPrepareIds)) || []

      // Người tạo phiếu
      const listMemApproveIds = lstOutbound.map((val) => val.approvedBy)
      const memberApprove: any[] = (await omsApiHelper.getMemberByListId(req, listMemApproveIds)) || []

      const mappingResult = lstOutbound.map((val) => {
        const memCreate: any = memberCreate.find((m: any) => m.id === val.createdBy)
        const memPrepare: any = memberPrepare.find((m: any) => m.id === val.preparedBy)
        const memApproved: any = memberApprove.find((m: any) => m.id === val.approvedBy)
        return {
          ...val,
          createdByName: memCreate?.fullName,
          preparedByName: memPrepare?.fullName,
          approvedByName: memApproved?.fullName,
        }
      })

      return { data: mappingResult, total }
    } catch (error) {
      throw new Error(error)
    }
  }

  async paginationForMBCHistoryView(data: PaginationDto, req: IRequest) {
    try {
      const whereCon: any = {}
      if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
      if (data.where.orderCode) whereCon.orderCode = Like(`%${data.where.orderCode}%`)
      if (data.where.customerName) whereCon.customerName = Like(`%${data.where.customerName}%`)
      if (data.where.warehouseId) whereCon.warehouseId = data.where.warehouseId

      if (data.where.employeeId) whereCon.employeeId = data.where.employeeId
      whereCon.outboundDetails = {}
      whereCon.outboundDetails = {}
      if (data.where.productCode) {
        whereCon.outboundDetails.productCode = Like(`%${data.where.productCode}%`)
      }
      if (data.where.productName) {
        whereCon.outboundDetails.productName = Like(`%${data.where.productName}%`)
      }
      if (data.where.type) whereCon.type = data.where.type
      if (data.where.createdAt) {
        const formattedDate = new Date(data.where.createdAt).toISOString().split('T')[0]
        whereCon.createdAt = Raw((alias) => `DATE(${alias}) = '${formattedDate}'`)
      }
      if (data.where.approvedDate) {
        const formattedDate = new Date(data.where.approvedDate).toISOString().split('T')[0]
        whereCon.approvedDate = Raw((alias) => `DATE(${alias}) = '${formattedDate}'`)
      }

      if (data.where.shippedDate) {
        const formattedDate = new Date(data.where.shippedDate).toISOString().split('T')[0]
        whereCon.approvedDate = Raw((alias) => `DATE(${alias}) = '${formattedDate}'`)
      }
      // Tìm người xác nhận soạn hàng
      if (data.where.preparedByName) {
        // let lstPreparedUserId: string[] = await authApiHelper.getLstUserIdByName(req, { name: data.where.preparedByName })
        // if (lstPreparedUserId.length > 0) whereCon.preparedBy = In(lstPreparedUserId)
        // else return [[], 0]
      }
      // Tìm người xác nhận soạn hàng
      if (data.where.approvedByName) {
        // let lstApproveUserId: string[] = await authApiHelper.getLstUserIdByName(req, { name: data.where.approvedByName })
        // if (lstApproveUserId.length > 0) whereCon.approvedBy = In(lstApproveUserId)
        // else return [[], 0]
      }

      if (data.where.brandId) {
        whereCon.outboundDetails.product.brandId = Like(`%${data.where.brandId}%`)
      }

      // ...data.where?.warehouseType && { warehouse: { type: data.where.warehouseType } },
      if (data.where.warehouseType) {
        whereCon.warehouse = { type: data.where.warehouseType }
      }

      let whereOr: any[] = []
      if (data.where.status) {
        whereCon.status = In(data.where.status)
        whereOr = [{ ...whereCon, warehouse: { type: NSWarehouse.EWarehouseType.MBC } }]
      } else {
        const whereConLst: any = { ...whereCon }
        whereConLst.status = In(data.where.lstStatus)
        whereOr = [
          { ...whereCon, warehouse: { type: NSWarehouse.EWarehouseType.MBC } },
          { ...whereConLst, warehouse: { type: NSWarehouse.EWarehouseType.MBC } },
        ]
      }

      const [lstOutbound, total]: any = await this.repo.findAndCount({
        where: whereOr,
        order: { createdAt: 'DESC' },
        skip: data.skip,
        take: data.take,
        relations: { warehouse: true },
      })
      for (let e of lstOutbound) {
        e.warehouseName = e.__warehouse__?.name ?? ''
        e.typeName = enumData.OutboundType[e.type]?.name ?? ''
        e.statusName = enumData.OutboundStatus[e.status]?.name ?? ''
        e.statusColor = enumData.OutboundStatus[e.status]?.color ?? ''
        delete e.__warehouse__
      }
      if (lstOutbound.length == 0) return { data: [], total: 0 }
      const listMemCreateIds = lstOutbound.map((val) => val.createdBy)
      const memberCreate: any[] = (await omsApiHelper.getMemberByListId(req, listMemCreateIds)) || []

      // Người tạo phiếu
      const listMemPrepareIds = lstOutbound.map((val) => val.preparedBy)
      const memberPrepare: any[] = (await omsApiHelper.getMemberByListId(req, listMemPrepareIds)) || []

      // Người tạo phiếu
      const listMemApproveIds = lstOutbound.map((val) => val.approvedBy)
      const memberApprove: any[] = (await omsApiHelper.getMemberByListId(req, listMemApproveIds)) || []

      const mappingResult = lstOutbound.map((val) => {
        const memCreate: any = memberCreate.find((m: any) => m.id === val.createdBy)
        const memPrepare: any = memberPrepare.find((m: any) => m.id === val.preparedBy)
        const memApproved: any = memberApprove.find((m: any) => m.id === val.approvedBy)
        return {
          ...val,
          createdByName: memCreate?.fullName,
          preparedByName: memPrepare?.fullName,
          approvedByName: memApproved?.fullName,
        }
      })

      return { data: mappingResult, total }
    } catch (error) {
      throw new Error(error)
    }
  }

  async createHistory(data: { description: string; outboundId: string }, manager: EntityManager) {
    const outboundHistory = new OutboundHistoryEntity()
    outboundHistory.createdAt = new Date()
    outboundHistory.outboundId = data.outboundId
    outboundHistory.description = data.description
    await manager.getRepository(OutboundHistoryEntity).insert(outboundHistory)
  }

  /** Hàm tạo mới phiếu xuất kho */
  async createData(data: OutboundCreateDto, manager?: EntityManager, req?: IRequest) {
    try {
      const checkWarehouse = await this.warehouseRepo.findOne({
        where: { id: data.warehouseId, isDeleted: false },
        select: { id: true, name: true },
      })
      if (!checkWarehouse) throw new Error(`Không tìm thấy kho vật lý hoặc kho vật lý đã bị ngưng hoạt động!`)

      const dataRs: any = data.lstOutboundDetail

      // Danh sách id sản phẩm
      for (const item of dataRs) {
        if (item.lstProductDetail)
          for (const lstDetail of item.lstProductDetail) {
            item.productDetailId = lstDetail.id
          }
        else {
          const product = await this.warehouseProductDetailRepo.findOne({
            where: { productId: item.productId, productDetailId: item.productDetailId, isDeleted: false, manufactureDate: Not(IsNull()) },
            order: { manufactureDate: 'DESC' },
          })
          item.productDetailId = product.productDetailId
          item.expiryDate = product.expiryDate
          item.manufactureDate = product.manufactureDate
        }
      }
      const lstProductId: string[] = dataRs.mapAndDistinct((detail) => detail.productId)
      const lstProductDetailId: string[] = dataRs.mapAndDistinct((detail) => detail.productDetailId)
      if (lstProductDetailId.length == 0) {
        throw new Error(`Không tìm thấy danh sách sản phẩm thực!`)
      }
      // Dic Product
      let dicProduct: any = {}
      {
        const lstProduct = await this.productRepo.find({
          where: { id: In(lstProductId) },
          select: { id: true, code: true, name: true, quantity: true, quantityLock: true },
        })
        dicProduct = coreHelper.arrayToObject(lstProduct)
      }

      const dicProductDetail: any = {}
      {
        let lstProductDetail: ItemDetailEntity[] = []
        lstProductDetail = await this.productDetailRepo.find({
          where: {
            id: In(lstProductDetailId),
          },
          select: { id: true, itemId: true, expiryDate: true, quantity: true, quantityLock: true, quantityLockEmp: true },
        })
        if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

        // Số lượng: quantity
        // Số lượng đã lên đơn tạm: quantityLock
        // Số lượng lock khi phân kho: quantityLockEmp

        for (let pd of lstProductDetail) {
          // Tạo dic dicProductDetail
          dicProductDetail[pd.id] = pd
        }
      }

      let dicWarehouseProduct: any = {}
      let dicWarehouseProductDetail: any = {}
      {
        const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
          where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: data.warehouseId, isDeleted: false },
          select: {
            id: true,
            warehouseProductId: true,
            warehouseId: true,
            productId: true,
            productDetailId: true,
            expiryDate: true,
            quantity: true,
            quantityLock: true,
            manufactureDate: true,
          },
        })
        let lstWarehouseProductId: string[] = []
        for (let wpd of lstWarehouseProductDetail) {
          dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
          lstWarehouseProductId.push(wpd.warehouseProductId)
        }

        const lstWarehouseProduct = await this.warehouseProductRepo.find({
          where: { id: In(lstWarehouseProductId), isDeleted: false },
          select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true },
        })

        for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
      }

      for (let obd of dataRs) {
        let productDetail = dicProductDetail[obd.productDetailId]
        if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

        const product = dicProduct[obd.productId]
        if (!product) throw new Error(`Không tìm thấy sản phẩm thực`)

        const warehouseProduct = dicWarehouseProduct[data.warehouseId + obd.productId]
        if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho [ ${checkWarehouse.name} ]!`)

        const warehouseProductDetail = dicWarehouseProductDetail[data.warehouseId + obd.productId + productDetail.id]
        if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho [ ${checkWarehouse.name} ]!`)

        // #region kiểm tra điều kiện trước khi trừ tồn kho
        {
          if (obd.quantity > +productDetail.quantity - +productDetail.quantityLock)
            if (!productDetail)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                  +productDetail.quantity - +productDetail.quantityLock
                }!`,
              )

          if (obd.quantity > +product.quantity - +product.quantityLock)
            if (!product)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${+product.quantity - +product.quantityLock}!`,
              )

          if (obd.quantity > +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock)
            if (!warehouseProductDetail)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                  +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock
                }!`,
              )

          if (obd.quantity > +warehouseProduct.quantity - +warehouseProduct.quantityLock)
            if (!warehouseProduct)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                  +warehouseProduct.quantity - +warehouseProduct.quantityLock
                }!`,
              )
        }

        // Gán lại data do bên order anh Thanh không lưu những thông tin này
        if (data.orderId) {
          obd.inventory = +warehouseProductDetail.quantity
          obd.lotNumber = productDetail.lotNumber
          obd.expiryDate = productDetail.expiryDate
        }
      }

      // #endregion

      // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001
      const curDate = moment(new Date()).format('DDMMYYYY')

      let genCode: string
      // Đếm số lượng phiếu xuất kho trong ngày
      let count = await this.repo.count({ where: { code: Like(`${curDate}_%`) }, select: { id: true } })
      if (!count) count = 0

      genCode = this.genCode(count)
      let entityManager: EntityManager
      if (!manager) entityManager = this.repo.manager
      else entityManager = manager

      return await entityManager.transaction(async (trans) => {
        const lstTask: OutboundDetailEntity[] = []
        const repo = trans.getRepository(OutboundEntity)
        const outboundDetailRepo = trans.getRepository(OutboundDetailEntity)

        const productRepo = trans.getRepository(ItemEntity)
        const productDetailRepo = trans.getRepository(ItemDetailEntity)
        const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
        const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)

        const outbound = new OutboundEntity()
        outbound.id = uuidv4()
        outbound.code = genCode
        outbound.type = data.type
        outbound.warehouseId = data.warehouseId
        outbound.employeeId = data.employeeId
        outbound.createdBy = data.createBy
        outbound.createdAt = new Date(data.createdAt)

        // #region phiếu xuất kho tạo từ order
        if (data.orderId) {
          outbound.orderCode = data?.orderCode ? data?.orderCode : null
          outbound.orderId = data.orderId
          outbound.customerName = data?.customerName ? data?.customerName : null
          outbound.preparedAt = new Date(data.preparationDate)
          outbound.preparedBy = data.preparedBy
          outbound.packageQuantity = +data.packageQuantity
          outbound.status = enumData.OutboundStatus.APPROVED.code // Đổi thành "đã duyệt" từ "đã soạn đơn" cho PXK tạo từ Order
          outbound.approvedBy = data.createBy

          // Trừ tồn kho
          for (let obd of dataRs) {
            let productDetail = dicProductDetail[obd.productDetailId]
            if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

            const product = dicProduct[obd.productId]
            if (!product) throw new Error(`Không tìm thấy sản phẩm thực`)

            const warehouseProduct = dicWarehouseProduct[data.warehouseId + obd.productId]
            if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho vật lý`)

            const warehouseProductDetail = dicWarehouseProductDetail[data.warehouseId + obd.productId + productDetail.id]
            if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho vật lý`)

            /** Kiểm tra nếu không đủ hàng */
            if (+(+productDetail.quantity - +obd.quantity) < 0) {
              let mustExport = +obd.quantity // Số lượng phải xuất
              const productsOtherInWhs = await this.warehouseProductDetailRepo.find({
                where: {
                  productId: Not(obd.productId),
                  warehouseId: data.warehouseId,
                  isDeleted: false,
                  quantity: MoreThan(0),
                },
                order: { manufactureDate: 'DESC' },
              })
              if (productsOtherInWhs.length > 0) {
                const availableQuantity = productsOtherInWhs.reduce((sum, item) => sum + +item.quantity, 0)
                if (availableQuantity < mustExport) {
                  throw new Error(`Không đủ tồn kho [Yêu cầu: ${obd.quantity} / Thực tế còn: ${availableQuantity}]`)
                }
                for (const productsOtherInWh of productsOtherInWhs) {
                  if (mustExport == 0) break
                  const productsOtherDetail = await this.productDetailRepo.findOne({
                    where: {
                      id: productsOtherInWh.productDetailId,
                      // isDeleted: false,
                      quantity: MoreThan(0),
                    },
                    order: { manufactureDate: 'DESC' },
                  })
                  mustExport -= +productsOtherDetail.quantity
                  await productDetailRepo.update(productsOtherDetail.id, {
                    quantity: +productsOtherDetail.quantity - +obd.quantity,
                    updatedAt: new Date(),
                    updatedBy: data.createBy,
                  })

                  const productOther = await productRepo.findOne({ where: { id: productsOtherDetail.itemId } })
                  await productRepo.update(productOther.id, {
                    quantity: +productOther.quantity - +obd.quantity,
                    updatedAt: new Date(),
                    updatedBy: data.createBy,
                  })

                  const whProductDetail = await this.warehouseProductDetailRepo.findOne({
                    where: { productDetailId: productsOtherInWh.productDetailId },
                  })
                  await warehouseProductDetailRepo.update(whProductDetail.id, {
                    quantity: +whProductDetail.quantity - +obd.quantity,
                    updatedAt: new Date(),
                    updatedBy: data.createBy,
                  })

                  const whProduct = await this.warehouseProductRepo.findOne({ where: { productId: productOther.id } })
                  await warehouseProductRepo.update(whProduct.id, {
                    quantity: +whProduct.quantity - +obd.quantity,
                    updatedAt: new Date(),
                    updatedBy: data.createBy,
                  })
                }
              } else {
                const product = await this.productRepo.findOne({ where: { id: obd.productId }, select: ['name', 'code'] })
                throw new Error(`Sản phẩm (${product.code}) không còn tồn kho!`)
              }
            } else {
              // #region Trừ bảng product và productDetail
              // Trừ của productDetail
              productDetail.quantity = +productDetail.quantity - +obd.quantity
              productDetail.updatedAt = new Date()
              productDetail.updatedBy = data.createBy
              await productDetailRepo.update(productDetail.id, productDetail)

              // Trừ của product
              product.quantity = +product.quantity - +obd.quantity
              product.updatedAt = new Date()
              product.updatedBy = data.createBy
              await productRepo.update(product.id, product)
              // #endregion

              // #region Trừ bảng warehouseProduct và warehouseProductDetail
              // Trừ của warehouseProductDetail
              warehouseProductDetail.quantity = +warehouseProductDetail.quantity - +obd.quantity
              warehouseProductDetail.updatedAt = new Date()
              warehouseProductDetail.updatedBy = data.createBy
              await warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail)

              // Trừ của warehouseProduct
              warehouseProduct.quantity = +warehouseProduct.quantity - +obd.quantity
              warehouseProduct.updatedAt = new Date()
              warehouseProduct.updatedBy = data.createBy
              await warehouseProductRepo.update(warehouseProduct.id, warehouseProduct)
              // #endregion
            }
          }
        }
        // #endregion

        // #region phiếu xuất kho tạo từ phiếu chuyển kho
        if (data.warehouseTransferId) {
          outbound.warehouseTransferId = data.warehouseTransferId
          outbound.status = enumData.OutboundStatus.APPROVED.code
          outbound.approvedBy = data.createBy
          outbound.approvedDate = new Date()
        }
        // #endregion

        outbound.createdBy = data.createBy
        outbound.description = data?.description ?? null
        await repo.insert(outbound)

        for (let detail of dataRs) {
          const outboundDetail = new OutboundDetailEntity()
          outboundDetail.outboundId = outbound.id
          outboundDetail.productId = detail.productId
          outboundDetail.productCode = dicProduct[detail.productId]?.code
          outboundDetail.productName = dicProduct[detail.productId]?.name
          outboundDetail.productDetailId = detail.productDetailId
          if (detail.expiryDate) outboundDetail.expiryDate = new Date(detail.expiryDate)
          outboundDetail.manufactureDate = detail?.manufactureDate ? new Date(detail.manufactureDate) : null
          outboundDetail.lotNumber = detail.lotNumber
          outboundDetail.inventory = detail.inventory
          outboundDetail.quantity = detail.quantity
          outboundDetail.createdBy = data.createBy
          lstTask.push(outboundDetail)
        }

        const member: any = await omsApiHelper.getMemberByListId(req, [data.createBy])

        await outboundDetailRepo.insert(lstTask)
        let description: string
        if (data.orderId) description = `Nhân viên ${member[0]?.fullName} tạo mới phiếu xuất kho từ đơn hàng`
        else description = `Nhân viên ${member[0]?.fullName} tạo mới phiếu xuất kho`

        await this.createHistory({ outboundId: outbound.id, description }, trans)

        return { outboundId: outbound.id, message: `Tạo mới phiếu xuất kho có mã {${outbound.code}} thành công` }
      })
    } catch (error) {
      console.log(error)
      throw new Error(error)
    }
  }

  /** Hàm chỉnh sửa phiếu xuất kho */
  async updateData(data: OutboundUpdateDto, req?: IRequest) {
    const checkWarehouse = await this.warehouseRepo.findOne({ where: { id: data.warehouseId, isDeleted: false }, select: { id: true } })
    if (!checkWarehouse) throw new Error(`Không tìm thấy kho vật lý hoặc kho vật lý đã bị ngưng hoạt động!`)

    const check = await this.repo.findOne({ where: { id: data.id, isDeleted: false }, select: { id: true, status: true } })
    if (!check) throw new Error(`Không tìm thấy phiếu xuất kho hoặc phiếu xuất kho đã bị ngưng hoạt động!`)

    if (check.status != enumData.OutboundStatus.NEW.code)
      throw new Error(`Chỉ có thể chỉnh sửa phiếu xuất kho ở trạng thái [ ${enumData.OutboundStatus.NEW.code} ]`)

    return await this.repo.manager.transaction(async (trans) => {
      const lstTask: OutboundDetailEntity[] = []
      const repo = trans.getRepository(OutboundEntity)
      const outboundDetailRepo = trans.getRepository(OutboundDetailEntity)
      check.type = data.type
      check.warehouseId = data.warehouseId
      check.employeeId = data.employeeId
      check.orderId = data.orderId
      check.updatedBy = data.updateBy
      check.description = data?.description ?? null
      check.updatedAt = new Date(data.updatedAt)
      await repo.update(check.id, check)

      // Xoá sản phẩm trong phiếu xuất kho
      await outboundDetailRepo.delete({ outboundId: check.id })

      // Thêm sản phẩm vào phiếu xuất kho
      for (let detail of data.lstOutboundDetail) {
        const outboundDetail = new OutboundDetailEntity()
        outboundDetail.outboundId = check.id
        outboundDetail.productId = detail.productId
        outboundDetail.productDetailId = detail.productDetailId
        outboundDetail.expiryDate = new Date(detail.expiryDate)
        outboundDetail.manufactureDate = new Date(detail.manufactureDate)
        outboundDetail.lotNumber = detail.lotNumber
        outboundDetail.inventory = detail.inventory
        outboundDetail.quantity = detail.quantity
        outboundDetail.createdBy = data.updateBy
        outboundDetail.updatedBy = data.updateBy
        lstTask.push(outboundDetail)
      }

      const member: any = await omsApiHelper.getMemberByListId(req, [data.updateBy])

      await outboundDetailRepo.insert(lstTask)
      const description = `Nhân viên ${member[0]?.fullName} cập nhật phiếu xuất kho`
      await this.createHistory({ outboundId: check.id, description }, trans)

      return { message: `Cập nhật phiếu xuất kho có mã {${check.code}} thành công` }
    })
  }

  /** Hàm tạo mới phiếu xuất kho tạo combo */
  async createDataCombo(data: OutboundCreateComboDto, req: IRequest, user?: UserDto) {
    const checkWarehouse = await this.warehouseRepo.findOne({ where: { id: data.warehouseId, isDeleted: false }, select: { id: true } })
    if (!checkWarehouse) throw new Error(`Không tìm thấy kho vật lý hoặc kho vật lý đã bị ngưng hoạt động!`)

    let dicProductCombo: any = {}
    let dicProductInComboId: any = {}
    let dicProductInComboPrice: any = {}
    {
      const lstProductComboId = data.lstOutboundComboDetail.map((e: any) => e.productId)
      const lstProductCombo: any = await this.productRepo.find({
        where: { id: In(lstProductComboId), isCombo: true },
        relations: { itemCombo: true },
        select: {
          id: true,
          code: true,
          name: true,
          quantity: true,
          itemCombo: { id: true, quantity: true, itemId: true, itemInComboId: true },
        },
      })
      let lstProductInComboId: string[] = []
      for (let prodCombo of lstProductCombo) {
        prodCombo.lstProductInProductCombo = prodCombo.__itemCombo__

        for (let pipc of prodCombo.__itemCombo__) {
          lstProductInComboId.push(pipc.itemInComboId)
        }
        delete prodCombo.__itemCombo__
      }

      const lstProductInCombo = await this.productRepo.find({
        where: { id: In(lstProductInComboId) },
        select: { id: true, code: true, name: true },
      })
      dicProductInComboId = coreHelper.arrayToObject(lstProductInCombo)
      dicProductCombo = coreHelper.arrayToObject(lstProductCombo)

      const lstProductPrice: any[] = await this.productPriceRepo.find({ where: { itemId: In(lstProductInComboId), isDeleted: false } })
      dicProductInComboPrice = coreHelper.arrayToObject(lstProductPrice, 'itemId')
    }

    // #region Check tồn kho

    let lstProductId: string[] = []
    let lstExpiryDate: any[] = []
    let lstProductDetailId: string[] = []

    // #region thêm hạn sử dụng và ngày sản xuất cho sp combo
    for (let prodCombo of data.lstOutboundComboDetail) {
      lstProductId.push(prodCombo.productId)
      let closestManufactureDate: Date = new Date(prodCombo.lstOutboundDetail[0].manufactureDate)
      let closetExpiryDate: Date = new Date(prodCombo.lstOutboundDetail[0].expiryDate)

      for (let pro of prodCombo.lstOutboundDetail) {
        lstProductId.push(pro.productId)
        lstProductDetailId.push(pro.productDetailId)
        lstExpiryDate.push(moment(pro.expiryDate).format('YYYY-MM-DD'))

        if (closestManufactureDate.getTime() > new Date(pro.manufactureDate).getTime()) {
          closestManufactureDate = new Date(pro.manufactureDate)
        }

        if (closetExpiryDate.getTime() > new Date(pro.expiryDate).getTime()) {
          closetExpiryDate = new Date(pro.expiryDate)
        }
      }

      prodCombo.expiryDate = closetExpiryDate
      prodCombo.manufactureDate = closestManufactureDate
    }

    // Dic Product
    let dicProduct: any = {}
    {
      const lstProduct = await this.productRepo.find({
        where: { id: In(lstProductId), isDeleted: false },
        select: { id: true, code: true, name: true, quantity: true, quantityLock: true, quantityLockEmp: true },
      })
      dicProduct = coreHelper.arrayToObject(lstProduct)
    }

    const dicProductDetail: any = {}
    {
      if (!lstProductId || lstProductId.length === 0) {
        throw new Error('Danh sách sản phẩm trống')
      }
      if (!lstExpiryDate || lstExpiryDate.length === 0) {
        throw new Error('Danh sách ngày hết hạn trống')
      }

      if (!lstProductDetailId || lstProductDetailId.length === 0) {
        throw new Error('Danh sách chi tiết sản phẩm không được để trống')
      }

      const validProductDetailIds = lstProductDetailId.filter((id) => id && id.trim() !== '')
      if (validProductDetailIds.length === 0) {
        throw new Error('Danh sách chi tiết sản phẩm không hợp lệ')
      }

      let lstProductDetail: ItemDetailEntity[] = await this.productDetailRepo.find({
        where: [
          {
            itemId: In(lstProductId),
            expiryDate: In(lstExpiryDate.map((date) => moment(date).format('YYYY-MM-DD'))),
          },
          {
            id: In(validProductDetailIds),
          },
        ],
        select: {
          id: true,
          itemId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
          quantityLockEmp: true,
          lotNumber: true,
          sellPrice: true,
        },
      })
      if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

      // Số lượng: quantity
      // Số lượng đã lên đơn tạm: quantityLock
      // Số lượng lock khi phân kho: quantityLockEmp
      lstProductDetailId = []

      for (let pd of lstProductDetail) {
        // Push id vô lstProductDetailId
        lstProductDetailId.push(pd.id)

        dicProductDetail[pd.id] = pd
      }
    }

    let dicWarehouseProduct: any = {}
    let dicWarehouseProductDetail: any = {}
    {
      const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
        where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: data.warehouseId, isDeleted: false },
        select: {
          id: true,
          warehouseProductId: true,
          warehouseId: true,
          productId: true,
          productDetailId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
        },
      })
      let lstWarehouseProductId: string[] = []
      for (let wpd of lstWarehouseProductDetail) {
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + +moment(wpd.expiryDate).format('YYYY-MM-DD')] = wpd
        lstWarehouseProductId.push(wpd.warehouseProductId)
      }

      const lstWarehouseProduct = await this.warehouseProductRepo.find({
        where: { id: In(lstWarehouseProductId), isDeleted: false },
        select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true },
      })

      for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
    }

    for (let obd of data.lstOutboundComboDetail) {
      const productCombo = dicProductCombo[obd.productId]
      if (!productCombo) throw new Error(`Không tìm thấy sản phẩm combo!`)

      // let productComboDetail = dicProductDetail[obd.productId + obd.expiryDate]
      // if (!productComboDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

      {
        // Nhân số lượng các sản phẩm con của sản phẩm combo với số lượng sản phẩm combo tương ứng
        for (let pipc of productCombo.lstProductInProductCombo) {
          pipc.quantity = +pipc.quantity * +obd.quantity
        }
      }

      // #region Danh sách sản phẩm con của sản phẩm combo
      for (let prod of obd.lstOutboundDetail) {
        const product = dicProduct[prod.productId]
        if (!product) throw new Error(`Không tìm thấy sản phẩm thực!`)

        let productDetail = dicProductDetail[prod.productDetailId]
        if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

        const warehouseProduct = dicWarehouseProduct[data.warehouseId + prod.productId]
        if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho vật lý`)

        const warehouseProductDetail = dicWarehouseProductDetail[data.warehouseId + prod.productId + productDetail.id]
        if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho vật lý`)

        // #region kiểm tra đủ số lượng
        {
          for (let pipc of productCombo.lstProductInProductCombo) {
            // Trừ đi số lượng nếu trùng id sản phẩm
            if (pipc.productInComboId == prod.productId) {
              pipc.quantity = +pipc.quantity - +prod.quantity
            }
          }
        }

        // #endregion

        // #region kiểm tra điều kiện trước khi trừ tồn kho
        {
          if (prod.quantity > +productDetail.quantity - +productDetail.quantityLock)
            if (!productDetail)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${
                  +productDetail.quantity - +productDetail.quantityLock
                }!`,
              )

          if (prod.quantity > +product.quantity - +product.quantityLock)
            if (!product)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${+product.quantity - +product.quantityLock}!`,
              )

          if (prod.quantity > +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock)
            if (!warehouseProductDetail)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${
                  +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock
                }!`,
              )

          if (prod.quantity > +warehouseProduct.quantity - +warehouseProduct.quantityLock)
            if (!warehouseProduct)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${
                  +warehouseProduct.quantity - +warehouseProduct.quantityLock
                }!`,
              )
        }
      }
      // #endregion

      // #region kiểm tra đủ số lượng
      {
        const findInvalidQuantity = productCombo.lstProductInProductCombo.find((pipc: any) => pipc.quantity == 0)
        if (findInvalidQuantity) throw new Error(`Sản phẩm [ ${dicProductInComboId[findInvalidQuantity.itemInComboId]?.code} ] chưa đủ số lượng!`)
      }

      // #endregion
    }

    // #endregion

    // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001
    const curDate = moment(new Date()).format('DDMMYYYY')

    let genCode: string
    // Đếm số lượng phiếu xuất kho trong ngày
    let count = await this.repo.count({ where: { code: Like(`${curDate}_%`) }, select: { id: true } })
    if (!count) count = 0

    genCode = this.genCode(count)

    const member: any = await omsApiHelper.getMemberByListId(req, [data.createBy])

    return await this.repo.manager.transaction(async (trans) => {
      const lstTask: OutboundDetailEntity[] = []
      const repo = trans.getRepository(OutboundEntity)
      const outboundDetailRepo = trans.getRepository(OutboundDetailEntity)
      const productRepo = trans.getRepository(ItemEntity)
      const productDetailRepo = trans.getRepository(ItemDetailEntity)
      const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)
      const minusEmployeeProductQuantityDto: MinusEmployeeProductQuantityDto = { employeeId: data.employeeId, lstProductInfo: [] }
      const productPriceRepo = trans.getRepository(ItemPriceEntity)
      const outbound = new OutboundEntity()
      outbound.id = uuidv4()
      outbound.code = genCode
      outbound.type = data.type
      outbound.status = enumData.OutboundStatus.NEW.code
      outbound.warehouseId = data.warehouseId
      outbound.employeeId = data.employeeId
      outbound.createdAt = new Date(data.createdAt)
      outbound.createdBy = data.createBy
      outbound.description = data?.description || null
      await repo.insert(outbound)

      for (let detail of data.lstOutboundComboDetail) {
        const outboundComboDetail = new OutboundDetailEntity()
        outboundComboDetail.id = uuidv4()
        outboundComboDetail.outboundId = outbound.id
        outboundComboDetail.productId = detail.productId
        outboundComboDetail.isCombo = true
        // outboundComboDetail.productDetailId = detail.productDetailId
        outboundComboDetail.expiryDate = new Date(detail.expiryDate)
        outboundComboDetail.manufactureDate = detail?.manufactureDate ? new Date(detail.manufactureDate) : null
        // outboundComboDetail.lotNumber = detail.lotNumber
        // outboundComboDetail.inventory = detail.inventory
        outboundComboDetail.quantity = detail.quantity
        outboundComboDetail.createdBy = data.createBy
        lstTask.push(outboundComboDetail)

        detail.costPrice = 0

        for (let prod of detail.lstOutboundDetail) {
          // detail.costPrice += dicProductDetail[prod.productDetailId].sellPrice
          const outboundDetail = new OutboundDetailEntity()
          outboundDetail.outboundId = outbound.id
          outboundDetail.outboundDetailComboId = outboundComboDetail.id
          outboundDetail.productId = prod.productId
          outboundDetail.productCode = dicProduct[detail.productId]?.code
          outboundDetail.productName = dicProduct[detail.productId]?.name
          outboundDetail.productDetailId = prod.productDetailId
          outboundDetail.expiryDate = new Date(prod.expiryDate)
          outboundDetail.manufactureDate = prod?.manufactureDate ? new Date(prod.manufactureDate) : null
          outboundDetail.lotNumber = prod.lotNumber
          outboundDetail.inventory = prod.inventory
          outboundDetail.quantity = prod.quantity
          outboundDetail.createdBy = data.createBy
          lstTask.push(outboundDetail)

          detail.costPrice += (+dicProductInComboPrice[prod.productId]?.priceCapital || 0) * +prod.quantity

          // #region Trừ tồn kho sp con trong combo
          // {
          const productDetail = dicProductDetail[prod.productDetailId]
          const product = dicProduct[prod.productId]
          const warehouseProduct = dicWarehouseProduct[data.warehouseId + prod.productId]
          const warehouseProductDetail = dicWarehouseProductDetail[data.warehouseId + prod.productId + productDetail.id]

          // #region Trừ bảng product và productDetail
          //   // Trừ của productDetail
          //   productDetail.quantity = +productDetail.quantity - +prod.quantity
          //   if (productDetail.quantityLockEmp > 0 || productDetail.quantityLockEmp - +prod.quantity > 0) {
          //     productDetail.quantityLockEmp = +productDetail.quantityLockEmp - +prod.quantity
          //   }

          //   productDetail.updatedAt = new Date()
          //   productDetail.updatedBy = user?.id
          //   await productDetailRepo.update(productDetail.id, productDetail)

          //   // Trừ của product
          //   product.quantity = +product.quantity - +prod.quantity

          //   if (product.quantityLockEmp > 0 || product.quantityLockEmp - +prod.quantity > 0) {
          //     product.quantityLockEmp = +product.quantityLockEmp - +prod.quantity
          //   }

          //   product.updatedAt = new Date()
          //   product.updatedBy = user?.id
          //   await productRepo.update(product.id, product)
          //   // #endregion

          //   // #region Trừ bảng warehouseProduct và warehouseProductDetail
          //   // Trừ của warehouseProductDetail
          //   warehouseProductDetail.quantity = +warehouseProductDetail.quantity - +prod.quantity
          //   warehouseProductDetail.updatedAt = new Date()
          //   warehouseProductDetail.updatedBy = user?.id
          //   await warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail)

          //   // Trừ của warehouseProduct
          //   warehouseProduct.quantity = +warehouseProduct.quantity - +prod.quantity
          //   warehouseProduct.updatedAt = new Date()
          //   warehouseProduct.updatedBy = user?.id
          //   await warehouseProductRepo.update(warehouseProduct.id, warehouseProduct)
          //   // #endregion

          //   minusEmployeeProductQuantityDto.lstProductInfo.push({
          //     productId: prod.productId,
          //     productDetailId: prod.productDetailId,
          //     quantity: prod.quantity,
          //   })
          // }
          // #endregion
        }

        // #region Cộng tồn kho vật lý (Bảng product và productDetail) cho sp combo
        // {
        //   const productCombo = dicProduct[outboundComboDetail.productId]
        //   let productComboDetail: any = dicProductDetail[outboundComboDetail.productId + moment(outboundComboDetail.expiryDate).format('YYYY-MM-DD')]

        //   // #region Cộng bảng product và productDetail

        //   // Cộng của productDetail
        //   if (productComboDetail) {
        //     productComboDetail.quantity = +productComboDetail.quantity + +detail.quantity
        //     productComboDetail.quantityLockEmp = +productComboDetail.quantityLockEmp + +detail.quantity
        //     productComboDetail.updatedAt = new Date()
        //     productComboDetail.updatedBy = user?.id
        //     await productDetailRepo.update(productComboDetail.id, productComboDetail)
        //   } else {
        //     productComboDetail = new ProductDetailEntity()
        //     productComboDetail.id = uuidv4()
        //     productComboDetail.productId = detail.productId
        //     productComboDetail.quantity = detail.quantity
        //     productComboDetail.manufactureDate = detail.manufactureDate
        //     productComboDetail.expiryDate = detail.expiryDate
        //     productComboDetail.costPrice = +detail.costPrice
        //     productComboDetail.quantityLockEmp = detail.quantity
        //     productComboDetail.createdAt = new Date()
        //     productComboDetail.createdBy = user?.id
        //     await productDetailRepo.insert(productComboDetail)
        //   }

        //   // Cộng của product
        //   productCombo.quantity = +productCombo.quantity + +detail.quantity
        //   productCombo.quantityLockEmp = +productCombo.quantityLockEmp + +detail.quantity
        //   productCombo.updatedAt = new Date()
        //   productCombo.updatedBy = user?.id
        //   await productRepo.update(productCombo.id, productCombo)
        //   // #endregion

        //   let warehouseProduct: any = dicWarehouseProduct[data.warehouseId + outboundComboDetail.productId]
        //   if (!warehouseProduct) {
        //     warehouseProduct = new WarehouseProductEntity()
        //     warehouseProduct.id = uuidv4()
        //     warehouseProduct.warehouseId = data.warehouseId
        //     warehouseProduct.productId = outboundComboDetail.productId
        //     warehouseProduct.quantity = outboundComboDetail.quantity
        //     warehouseProduct.createdAt = new Date()
        //     warehouseProduct.createdBy = user?.id
        //     await warehouseProductRepo.insert(warehouseProduct)
        //   } else {
        //     const quantityOld = +warehouseProduct.quantity
        //     const quantityNew = quantityOld + +outboundComboDetail.quantity
        //     warehouseProduct.quantity = quantityNew
        //     await warehouseProductRepo.update(warehouseProduct.id, warehouseProduct)
        //   }

        //   let warehouseProductDetail: any =
        //     dicWarehouseProductDetail[data.warehouseId + outboundComboDetail.productId + +moment(outboundComboDetail.expiryDate).format('YYYY-MM-DD')]
        //   if (!warehouseProductDetail) {
        //     const warehouseProductDetail = new WarehouseProductDetailEntity()
        //     warehouseProductDetail.id = uuidv4()
        //     warehouseProductDetail.warehouseId = data.warehouseId
        //     warehouseProductDetail.productId = outboundComboDetail.productId
        //     warehouseProductDetail.productDetailId = productComboDetail.id
        //     warehouseProductDetail.warehouseProductId = warehouseProduct.id
        //     warehouseProductDetail.quantity = outboundComboDetail.quantity
        //     warehouseProductDetail.manufactureDate = outboundComboDetail?.manufactureDate || null
        //     warehouseProductDetail.expiryDate = outboundComboDetail.expiryDate
        //     warehouseProductDetail.createdAt = new Date()
        //     warehouseProductDetail.createdBy = user?.id
        //     await warehouseProductDetailRepo.insert(warehouseProductDetail)
        //   } else {
        //     const quantityDetailNew = +warehouseProductDetail.quantity + +outboundComboDetail.quantity
        //     warehouseProductDetail.quantity = quantityDetailNew
        //     warehouseProductDetail = await warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail)
        //   }

        //   const productPriceObj = await productPriceRepo.findOne({
        //     where: { productId: productCombo.id, isFinal: true },
        //     order: { createdAt: 'DESC' },
        //   })
        //   if (productPriceObj) {
        //     await productPriceRepo.update(
        //       {
        //         productId: productCombo.id,
        //         id: productPriceObj.id,
        //       },
        //       {
        //         priceCapital: +detail.costPrice,
        //       },
        //     )
        //   }
        // }
        // #endregion
      }
      // #endregion

      // Trừ tồn kho nhân viên
      // await authApiHelper.minusEmployeeProductQuantity(req, minusEmployeeProductQuantityDto)

      await outboundDetailRepo.insert(lstTask)
      let description: string
      if (data.orderId) description = `Nhân viên ${member[0]?.fullName} tạo mới phiếu xuất kho từ đơn hàng`
      else description = `Nhân viên ${member[0]?.fullName} tạo mới phiếu xuất kho`

      await this.createHistory({ outboundId: outbound.id, description }, trans)

      // #region Tạo phiếu nhập kho cho sản phẩm combo

      // let lstDetail: any[] = []

      // for (let wtd of data.lstOutboundComboDetail) {
      //   lstDetail.push({
      //     productId: wtd.productId,
      //     productName: dicProductCombo[wtd.productId].name,
      //     productCode: dicProductCombo[wtd.productId].code,
      //     unitId: null,
      //     expiryDate: wtd.expiryDate,
      //     manufactureDate: wtd.manufactureDate,
      //     lotNumber: null,
      //     inventory: null,
      //     quantity: wtd.quantity,
      //     costPrice: wtd.costPrice,
      //   })
      // }

      // const inboundCreateDto: any = {
      //   type: enumData.InboundType.COMBO.code,
      //   warehouseId: outbound.warehouseId,
      //   lstDetail: lstDetail,
      // }

      // await this.inboundService.createDataApproved(user, inboundCreateDto, req, trans)
      // #endregion

      return { message: `Tạo mới phiếu xuất kho có mã [ ${outbound.code} ] thành công` }
    })
  }

  /** Hàm chỉnh sửa phiếu xuất kho tạo combo */
  async updateDataCombo(data: OutboundUpdateComboDto, req: IRequest, user?: UserDto) {
    const check = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!check) throw new Error(`Không tìm thấy phiếu xuất kho tạo combo!`)

    if (check.status != enumData.OutboundStatus.NEW.code)
      throw new Error(`Chỉ có thể chỉnh sửa phiếu xuất kho ở trạng thái [ ${enumData.OutboundStatus.NEW.name} ]!`)

    const checkWarehouse = await this.warehouseRepo.findOne({ where: { id: data.warehouseId, isDeleted: false }, select: { id: true } })
    if (!checkWarehouse) throw new Error(`Không tìm thấy kho vật lý hoặc kho vật lý đã bị ngưng hoạt động!`)

    let dicProductCombo: any = {}
    let dicProductInComboId: any = {}
    let dicProductInComboPrice: any = {}
    {
      const lstProductComboId = data.lstOutboundComboDetail.map((e: any) => e.productId)
      const lstProductCombo: any = await this.productRepo.find({
        where: { id: In(lstProductComboId), isCombo: true },
        relations: { itemCombo: true },
        select: {
          id: true,
          code: true,
          name: true,
          quantity: true,
          itemCombo: { id: true, quantity: true, itemId: true, itemInComboId: true },
        },
      })
      let lstProductInComboId: string[] = []
      for (let prodCombo of lstProductCombo) {
        prodCombo.lstProductInProductCombo = prodCombo.__itemCombo__

        for (let pipc of prodCombo.__itemCombo__) {
          lstProductInComboId.push(pipc.productInComboId)
        }
        delete prodCombo.__itemCombo__
      }

      const lstProductInCombo = await this.productRepo.find({
        where: { id: In(lstProductInComboId), isDeleted: false },
        select: { id: true, code: true, name: true },
      })
      dicProductInComboId = coreHelper.arrayToObject(lstProductInCombo)
      dicProductCombo = coreHelper.arrayToObject(lstProductCombo)

      const lstProductPrice: any[] = await this.productPriceRepo.find({ where: { itemId: In(lstProductInComboId), isDeleted: false } })
      dicProductInComboPrice = coreHelper.arrayToObject(lstProductPrice, 'itemId')
    }

    // #region Check tồn kho

    let lstProductId: string[] = []
    let lstExpiryDate: any[] = []
    let lstProductDetailId: string[] = []

    // #region thêm hạn sử dụng và ngày sản xuất cho sp combo
    for (let prodCombo of data.lstOutboundComboDetail) {
      lstProductId.push(prodCombo.productId)
      let closestManufactureDate: Date = new Date(prodCombo.lstOutboundDetail[0].manufactureDate)
      let closetExpiryDate: Date = new Date(prodCombo.lstOutboundDetail[0].expiryDate)

      for (let pro of prodCombo.lstOutboundDetail) {
        lstProductId.push(pro.productId)
        lstProductDetailId.push(pro.productDetailId)
        lstExpiryDate.push(moment(pro.expiryDate).format('YYYY-MM-DD'))

        if (closestManufactureDate.getTime() > new Date(pro.manufactureDate).getTime()) {
          closestManufactureDate = new Date(pro.manufactureDate)
        }

        if (closetExpiryDate.getTime() > new Date(pro.expiryDate).getTime()) {
          closetExpiryDate = new Date(pro.expiryDate)
        }
      }

      prodCombo.expiryDate = closetExpiryDate
      prodCombo.manufactureDate = closestManufactureDate
    }

    // Dic Product
    let dicProduct: any = {}
    {
      const lstProduct = await this.productRepo.find({
        where: { id: In(lstProductId) },
        select: { id: true, code: true, name: true, quantity: true, quantityLock: true, quantityLockEmp: true },
      })
      dicProduct = coreHelper.arrayToObject(lstProduct)
    }

    const dicProductDetail: any = {}
    {
      let lstProductDetail: ItemDetailEntity[] = await this.productDetailRepo.find({
        where: [
          {
            itemId: In(lstProductId),
            expiryDate: Raw(
              (alias) => `DATE(${alias}) IN (${lstExpiryDate.map((date) => `DATE("${moment(date).format('YYYY-MM-DD')}")`).join(', ')})`,
            ),
          },
          {
            id: In(lstProductDetailId),
          },
        ],
        select: {
          id: true,
          itemId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
          quantityLockEmp: true,
          lotNumber: true,
          sellPrice: true,
        },
      })
      if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

      // Số lượng: quantity
      // Số lượng đã lên đơn tạm: quantityLock
      // Số lượng lock khi phân kho: quantityLockEmp
      lstProductDetailId = []

      for (let pd of lstProductDetail) {
        // Push id vô lstProductDetailId
        lstProductDetailId.push(pd.id)

        dicProductDetail[pd.id] = pd
      }
    }

    let dicWarehouseProduct: any = {}
    let dicWarehouseProductDetail: any = {}
    {
      const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
        where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: data.warehouseId, isDeleted: false },
        select: {
          id: true,
          warehouseProductId: true,
          warehouseId: true,
          productId: true,
          productDetailId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
        },
      })
      let lstWarehouseProductId: string[] = []
      for (let wpd of lstWarehouseProductDetail) {
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + +moment(wpd.expiryDate).format('YYYY-MM-DD')] = wpd
        lstWarehouseProductId.push(wpd.warehouseProductId)
      }

      const lstWarehouseProduct = await this.warehouseProductRepo.find({
        where: { id: In(lstWarehouseProductId), isDeleted: false },
        select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true },
      })

      for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
    }

    for (let obd of data.lstOutboundComboDetail) {
      const productCombo = dicProductCombo[obd.productId]
      if (!productCombo) throw new Error(`Không tìm thấy sản phẩm combo!`)

      // let productComboDetail = dicProductDetail[obd.productId + obd.expiryDate]
      // if (!productComboDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

      {
        // Nhân số lượng các sản phẩm con của sản phẩm combo với số lượng sản phẩm combo tương ứng
        for (let pipc of productCombo.lstProductInProductCombo) {
          pipc.quantity = +pipc.quantity * +obd.quantity
        }
      }

      // #region Danh sách sản phẩm con của sản phẩm combo
      for (let prod of obd.lstOutboundDetail) {
        const product = dicProduct[prod.productId]
        if (!product) throw new Error(`Không tìm thấy sản phẩm thực!`)

        let productDetail = dicProductDetail[prod.productDetailId]
        if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

        const warehouseProduct = dicWarehouseProduct[data.warehouseId + prod.productId]
        if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho vật lý`)

        const warehouseProductDetail = dicWarehouseProductDetail[data.warehouseId + prod.productId + productDetail.id]
        if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho vật lý`)

        // #region kiểm tra đủ số lượng
        {
          for (let pipc of productCombo.lstProductInProductCombo) {
            // Trừ đi số lượng nếu trùng id sản phẩm
            if (pipc.productInComboId == prod.productId) {
              pipc.quantity = +pipc.quantity - +prod.quantity
            }
          }
        }

        // #endregion

        // #region kiểm tra điều kiện trước khi trừ tồn kho
        {
          if (prod.quantity > +productDetail.quantity - +productDetail.quantityLock)
            if (!productDetail)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${
                  +productDetail.quantity - +productDetail.quantityLock
                }!`,
              )

          if (prod.quantity > +product.quantity - +product.quantityLock)
            if (!product)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${+product.quantity - +product.quantityLock}!`,
              )

          if (prod.quantity > +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock)
            if (!warehouseProductDetail)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${
                  +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock
                }!`,
              )

          if (prod.quantity > +warehouseProduct.quantity - +warehouseProduct.quantityLock)
            if (!warehouseProduct)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${
                  +warehouseProduct.quantity - +warehouseProduct.quantityLock
                }!`,
              )
        }
      }
      // #endregion

      // #region kiểm tra đủ số lượng
      {
        const findInvalidQuantity = productCombo.lstProductInProductCombo.find((pipc: any) => pipc.quantity != 0)
        if (findInvalidQuantity) throw new Error(`Sản phẩm [ ${dicProductInComboId[findInvalidQuantity.productInComboId].code} ] chưa đủ số lượng!`)
      }

      // #endregion
    }

    // #endregion

    return await this.repo.manager.transaction(async (trans) => {
      const lstTask: OutboundDetailEntity[] = []
      const repo = trans.getRepository(OutboundEntity)
      const outboundDetailRepo = trans.getRepository(OutboundDetailEntity)

      check.warehouseId = data.warehouseId
      check.employeeId = data.employeeId
      check.updatedAt = new Date()
      check.updatedBy = user?.id
      check.description = data?.description || null
      await repo.update(check.id, check)

      // Xoá detail
      await outboundDetailRepo.delete({ outboundId: check.id })

      for (let detail of data.lstOutboundComboDetail) {
        const outboundComboDetail = new OutboundDetailEntity()
        outboundComboDetail.id = uuidv4()
        outboundComboDetail.outboundId = check.id
        outboundComboDetail.productId = detail.productId
        outboundComboDetail.isCombo = true
        // outboundComboDetail.productDetailId = detail.productDetailId
        outboundComboDetail.expiryDate = new Date(detail.expiryDate)
        outboundComboDetail.manufactureDate = detail?.manufactureDate ? new Date(detail.manufactureDate) : null
        // outboundComboDetail.lotNumber = detail.lotNumber
        // outboundComboDetail.inventory = detail.inventory
        outboundComboDetail.quantity = detail.quantity
        outboundComboDetail.createdBy = user?.id
        lstTask.push(outboundComboDetail)

        detail.costPrice = 0

        for (let prod of detail.lstOutboundDetail) {
          // detail.costPrice += dicProductDetail[prod.productDetailId].sellPrice
          const outboundDetail = new OutboundDetailEntity()
          outboundDetail.outboundId = check.id
          outboundDetail.outboundDetailComboId = outboundComboDetail.id
          outboundDetail.productId = prod.productId
          outboundDetail.productDetailId = prod.productDetailId
          outboundDetail.expiryDate = new Date(prod.expiryDate)
          outboundDetail.manufactureDate = prod?.manufactureDate ? new Date(prod.manufactureDate) : null
          outboundDetail.lotNumber = prod.lotNumber
          outboundDetail.inventory = prod.inventory
          outboundDetail.quantity = prod.quantity
          outboundDetail.createdBy = user?.id
          lstTask.push(outboundDetail)

          detail.costPrice += (+dicProductInComboPrice[prod.productId]?.priceCapital || 0) * +prod.quantity

          // #region Trừ tồn kho sp con trong combo
          // {
          const productDetail = dicProductDetail[prod.productDetailId]
          const product = dicProduct[prod.productId]
          const warehouseProduct = dicWarehouseProduct[data.warehouseId + prod.productId]
          const warehouseProductDetail = dicWarehouseProductDetail[data.warehouseId + prod.productId + productDetail.id]

          // #region Trừ bảng product và productDetail
          //   // Trừ của productDetail
          //   productDetail.quantity = +productDetail.quantity - +prod.quantity
          //   if (productDetail.quantityLockEmp > 0 || productDetail.quantityLockEmp - +prod.quantity > 0) {
          //     productDetail.quantityLockEmp = +productDetail.quantityLockEmp - +prod.quantity
          //   }

          //   productDetail.updatedAt = new Date()
          //   productDetail.updatedBy = user?.id
          //   await productDetailRepo.update(productDetail.id, productDetail)

          //   // Trừ của product
          //   product.quantity = +product.quantity - +prod.quantity

          //   if (product.quantityLockEmp > 0 || product.quantityLockEmp - +prod.quantity > 0) {
          //     product.quantityLockEmp = +product.quantityLockEmp - +prod.quantity
          //   }

          //   product.updatedAt = new Date()
          //   product.updatedBy = user?.id
          //   await productRepo.update(product.id, product)
          //   // #endregion

          //   // #region Trừ bảng warehouseProduct và warehouseProductDetail
          //   // Trừ của warehouseProductDetail
          //   warehouseProductDetail.quantity = +warehouseProductDetail.quantity - +prod.quantity
          //   warehouseProductDetail.updatedAt = new Date()
          //   warehouseProductDetail.updatedBy = user?.id
          //   await warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail)

          //   // Trừ của warehouseProduct
          //   warehouseProduct.quantity = +warehouseProduct.quantity - +prod.quantity
          //   warehouseProduct.updatedAt = new Date()
          //   warehouseProduct.updatedBy = user?.id
          //   await warehouseProductRepo.update(warehouseProduct.id, warehouseProduct)
          //   // #endregion

          //   minusEmployeeProductQuantityDto.lstProductInfo.push({
          //     productId: prod.productId,
          //     productDetailId: prod.productDetailId,
          //     quantity: prod.quantity,
          //   })
          // }
          // #endregion
        }

        // #region Cộng tồn kho vật lý (Bảng product và productDetail) cho sp combo
        // {
        //   const productCombo = dicProduct[outboundComboDetail.productId]
        //   let productComboDetail: any = dicProductDetail[outboundComboDetail.productId + moment(outboundComboDetail.expiryDate).format('YYYY-MM-DD')]

        //   // #region Cộng bảng product và productDetail

        //   // Cộng của productDetail
        //   if (productComboDetail) {
        //     productComboDetail.quantity = +productComboDetail.quantity + +detail.quantity
        //     productComboDetail.quantityLockEmp = +productComboDetail.quantityLockEmp + +detail.quantity
        //     productComboDetail.updatedAt = new Date()
        //     productComboDetail.updatedBy = user?.id
        //     await productDetailRepo.update(productComboDetail.id, productComboDetail)
        //   } else {
        //     productComboDetail = new ProductDetailEntity()
        //     productComboDetail.id = uuidv4()
        //     productComboDetail.productId = detail.productId
        //     productComboDetail.quantity = detail.quantity
        //     productComboDetail.manufactureDate = detail.manufactureDate
        //     productComboDetail.expiryDate = detail.expiryDate
        //     productComboDetail.costPrice = +detail.costPrice
        //     productComboDetail.quantityLockEmp = detail.quantity
        //     productComboDetail.createdAt = new Date()
        //     productComboDetail.createdBy = user?.id
        //     await productDetailRepo.insert(productComboDetail)
        //   }

        //   // Cộng của product
        //   productCombo.quantity = +productCombo.quantity + +detail.quantity
        //   productCombo.quantityLockEmp = +productCombo.quantityLockEmp + +detail.quantity
        //   productCombo.updatedAt = new Date()
        //   productCombo.updatedBy = user?.id
        //   await productRepo.update(productCombo.id, productCombo)
        //   // #endregion

        //   let warehouseProduct: any = dicWarehouseProduct[data.warehouseId + outboundComboDetail.productId]
        //   if (!warehouseProduct) {
        //     warehouseProduct = new WarehouseProductEntity()
        //     warehouseProduct.id = uuidv4()
        //     warehouseProduct.warehouseId = data.warehouseId
        //     warehouseProduct.productId = outboundComboDetail.productId
        //     warehouseProduct.quantity = outboundComboDetail.quantity
        //     warehouseProduct.createdAt = new Date()
        //     warehouseProduct.createdBy = user?.id
        //     await warehouseProductRepo.insert(warehouseProduct)
        //   } else {
        //     const quantityOld = +warehouseProduct.quantity
        //     const quantityNew = quantityOld + +outboundComboDetail.quantity
        //     warehouseProduct.quantity = quantityNew
        //     await warehouseProductRepo.update(warehouseProduct.id, warehouseProduct)
        //   }

        //   let warehouseProductDetail: any =
        //     dicWarehouseProductDetail[data.warehouseId + outboundComboDetail.productId + +moment(outboundComboDetail.expiryDate).format('YYYY-MM-DD')]
        //   if (!warehouseProductDetail) {
        //     const warehouseProductDetail = new WarehouseProductDetailEntity()
        //     warehouseProductDetail.id = uuidv4()
        //     warehouseProductDetail.warehouseId = data.warehouseId
        //     warehouseProductDetail.productId = outboundComboDetail.productId
        //     warehouseProductDetail.productDetailId = productComboDetail.id
        //     warehouseProductDetail.warehouseProductId = warehouseProduct.id
        //     warehouseProductDetail.quantity = outboundComboDetail.quantity
        //     warehouseProductDetail.manufactureDate = outboundComboDetail?.manufactureDate || null
        //     warehouseProductDetail.expiryDate = outboundComboDetail.expiryDate
        //     warehouseProductDetail.createdAt = new Date()
        //     warehouseProductDetail.createdBy = user?.id
        //     await warehouseProductDetailRepo.insert(warehouseProductDetail)
        //   } else {
        //     const quantityDetailNew = +warehouseProductDetail.quantity + +outboundComboDetail.quantity
        //     warehouseProductDetail.quantity = quantityDetailNew
        //     warehouseProductDetail = await warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail)
        //   }

        //   const productPriceObj = await productPriceRepo.findOne({
        //     where: { productId: productCombo.id, isFinal: true },
        //     order: { createdAt: 'DESC' },
        //   })
        //   if (productPriceObj) {
        //     await productPriceRepo.update(
        //       {
        //         productId: productCombo.id,
        //         id: productPriceObj.id,
        //       },
        //       {
        //         priceCapital: +detail.costPrice,
        //       },
        //     )
        //   }
        // }
        // #endregion
      }
      // #endregion

      // Trừ tồn kho nhân viên
      // await authApiHelper.minusEmployeeProductQuantity(req, minusEmployeeProductQuantityDto)

      await outboundDetailRepo.insert(lstTask)
      let description: string = `Nhân viên ${user?.username} chỉnh sửa phiếu xuất kho combo`

      await this.createHistory({ outboundId: check.id, description }, trans)

      // #region Tạo phiếu nhập kho cho sản phẩm combo

      // let lstDetail: any[] = []

      // for (let wtd of data.lstOutboundComboDetail) {
      //   lstDetail.push({
      //     productId: wtd.productId,
      //     productName: dicProductCombo[wtd.productId].name,
      //     productCode: dicProductCombo[wtd.productId].code,
      //     unitId: null,
      //     expiryDate: wtd.expiryDate,
      //     manufactureDate: wtd.manufactureDate,
      //     lotNumber: null,
      //     inventory: null,
      //     quantity: wtd.quantity,
      //     costPrice: wtd.costPrice,
      //   })
      // }

      // const inboundCreateDto: any = {
      //   type: enumData.InboundType.COMBO.code,
      //   warehouseId: outbound.warehouseId,
      //   lstDetail: lstDetail,
      // }

      // await this.inboundService.createDataApproved(user, inboundCreateDto, req, trans)
      // #endregion

      return { message: `Chỉnh sửa phiếu xuất kho tạo combo có mã [ ${check.code} ] thành công` }
    })
  }

  /** Hàm chỉnh sửa phiếu xuất kho tạo combo */
  async updateApproveCombo(data: FilterOneDto, req: IRequest, user?: UserDto) {
    const check: any = await this.repo.findOne({ where: { id: data.id, isDeleted: false }, relations: { outboundDetails: true } })
    if (!check) throw new Error(`Không tìm thấy phiếu xuất kho tạo combo!`)

    if (check.status != enumData.OutboundStatus.NEW.code)
      throw new Error(`Chỉ có thể duyệt phiếu xuất kho ở trạng thái [ ${enumData.OutboundStatus.NEW.name} ]!`)

    const lstOutboundComboDetail: any[] = []

    for (let cb of check.__outboundDetails__) {
      if (!cb.outboundDetailComboId) {
        cb.lstOutboundDetail = []
        for (let cbd of check.__outboundDetails__) {
          if (cbd.outboundDetailComboId == cb.id) cb.lstOutboundDetail.push(cbd)
        }
        lstOutboundComboDetail.push(cb)
      }
    }

    delete check.__outboundDetails__

    let dicProductCombo: any = {}
    let dicProductInComboId: any = {}
    let dicProductInComboPrice: any = {}
    {
      const lstProductComboId = lstOutboundComboDetail.map((e: any) => e.productId)
      const lstProductCombo: any = await this.productRepo.find({
        where: { id: In(lstProductComboId), isCombo: true },
        relations: { itemCombo: true },
        select: {
          id: true,
          code: true,
          name: true,
          quantity: true,
          itemCombo: { id: true, quantity: true, itemId: true, itemInComboId: true },
        },
      })
      let lstProductInComboId: string[] = []
      for (let prodCombo of lstProductCombo) {
        prodCombo.lstProductInProductCombo = prodCombo.__productCombo__

        for (let pipc of prodCombo.__productCombo__) {
          lstProductInComboId.push(pipc.productInComboId)
        }
        delete prodCombo.__productCombo__
      }

      const lstProductInCombo = await this.productRepo.find({
        where: { id: In(lstProductInComboId) },
        select: { id: true, code: true, name: true },
      })
      dicProductInComboId = coreHelper.arrayToObject(lstProductInCombo)
      dicProductCombo = coreHelper.arrayToObject(lstProductCombo)

      const lstProductPrice: any[] = await this.productPriceRepo.find({ where: { itemId: In(lstProductInComboId), isDeleted: false } })
      dicProductInComboPrice = coreHelper.arrayToObject(lstProductPrice, 'itemId')
    }

    // #region Check tồn kho

    let lstProductId: string[] = []
    let lstExpiryDate: any[] = []
    let lstProductDetailId: string[] = []

    // #region thêm hạn sử dụng và ngày sản xuất cho sp combo
    for (let prodCombo of lstOutboundComboDetail) {
      lstProductId.push(prodCombo.productId)
      let closestManufactureDate: Date = new Date(prodCombo.lstOutboundDetail[0].manufactureDate)
      let closetExpiryDate: Date = new Date(prodCombo.lstOutboundDetail[0].expiryDate)

      for (let pro of prodCombo.lstOutboundDetail) {
        lstProductId.push(pro.productId)
        lstProductDetailId.push(pro.productDetailId)
        lstExpiryDate.push(moment(pro.expiryDate).format('YYYY-MM-DD'))

        if (closestManufactureDate.getTime() > new Date(pro.manufactureDate).getTime()) {
          closestManufactureDate = new Date(pro.manufactureDate)
        }

        if (closetExpiryDate.getTime() > new Date(pro.expiryDate).getTime()) {
          closetExpiryDate = new Date(pro.expiryDate)
        }
      }

      prodCombo.expiryDate = closetExpiryDate
      prodCombo.manufactureDate = closestManufactureDate
    }

    // Dic Product
    let dicProduct: any = {}
    {
      const lstProduct = await this.productRepo.find({
        where: { id: In(lstProductId) },
        select: { id: true, code: true, name: true, quantity: true, quantityLock: true, quantityLockEmp: true },
      })
      dicProduct = coreHelper.arrayToObject(lstProduct)
    }

    const dicProductDetail: any = {}
    {
      let lstProductDetail: ItemDetailEntity[] = await this.productDetailRepo.find({
        where: [
          {
            itemId: In(lstProductId),
            expiryDate: Raw(
              (alias) => `DATE(${alias}) IN (${lstExpiryDate.map((date) => `DATE("${moment(date).format('YYYY-MM-DD')}")`).join(', ')})`,
            ),
          },
          {
            id: In(lstProductDetailId),
          },
        ],
        select: {
          id: true,
          itemId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
          quantityLockEmp: true,
          lotNumber: true,
          sellPrice: true,
        },
      })
      if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

      // Số lượng: quantity
      // Số lượng đã lên đơn tạm: quantityLock
      // Số lượng lock khi phân kho: quantityLockEmp
      lstProductDetailId = []

      for (let pd of lstProductDetail) {
        // Push id vô lstProductDetailId
        lstProductDetailId.push(pd.id)

        dicProductDetail[pd.id] = pd
      }
    }

    let dicWarehouseProduct: any = {}
    let dicWarehouseProductDetail: any = {}
    {
      const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
        where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: check.warehouseId, isDeleted: false },
        select: {
          id: true,
          warehouseProductId: true,
          warehouseId: true,
          productId: true,
          productDetailId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
        },
      })
      let lstWarehouseProductId: string[] = []
      for (let wpd of lstWarehouseProductDetail) {
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + +moment(wpd.expiryDate).format('YYYY-MM-DD')] = wpd
        lstWarehouseProductId.push(wpd.warehouseProductId)
      }

      const lstWarehouseProduct = await this.warehouseProductRepo.find({
        where: { id: In(lstWarehouseProductId), isDeleted: false },
        select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true },
      })

      for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
    }

    for (let obd of lstOutboundComboDetail) {
      const productCombo = dicProductCombo[obd.productId]
      if (!productCombo) throw new Error(`Không tìm thấy sản phẩm combo!`)

      // let productComboDetail = dicProductDetail[obd.productId + obd.expiryDate]
      // if (!productComboDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

      {
        // Nhân số lượng các sản phẩm con của sản phẩm combo với số lượng sản phẩm combo tương ứng
        for (let pipc of productCombo.lstProductInProductCombo) {
          pipc.quantity = +pipc.quantity * +obd.quantity
        }
      }

      // #region Danh sách sản phẩm con của sản phẩm combo
      for (let prod of obd.lstOutboundDetail) {
        const product = dicProduct[prod.productId]
        if (!product) throw new Error(`Không tìm thấy sản phẩm thực!`)

        let productDetail = dicProductDetail[prod.productDetailId]
        if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

        const warehouseProduct = dicWarehouseProduct[check.warehouseId + prod.productId]
        if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho vật lý`)

        const warehouseProductDetail = dicWarehouseProductDetail[check.warehouseId + prod.productId + productDetail.id]
        if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho vật lý`)

        // #region kiểm tra đủ số lượng
        {
          for (let pipc of productCombo.lstProductInProductCombo) {
            // Trừ đi số lượng nếu trùng id sản phẩm
            if (pipc.productInComboId == prod.productId) {
              pipc.quantity = +pipc.quantity - +prod.quantity
            }
          }
        }

        // #endregion

        // #region kiểm tra điều kiện trước khi trừ tồn kho
        {
          if (prod.quantity > +productDetail.quantity - +productDetail.quantityLock)
            if (!productDetail)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${
                  +productDetail.quantity - +productDetail.quantityLock
                }!`,
              )

          if (prod.quantity > +product.quantity - +product.quantityLock)
            if (!product)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${+product.quantity - +product.quantityLock}!`,
              )

          if (prod.quantity > +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock)
            if (!warehouseProductDetail)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${
                  +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock
                }!`,
              )

          if (prod.quantity > +warehouseProduct.quantity - +warehouseProduct.quantityLock)
            if (!warehouseProduct)
              throw new Error(
                `số lượng xuất của sản phẩm ${product.name} ${prod.quantity} lớn hơn số lượng tồn kho ${
                  +warehouseProduct.quantity - +warehouseProduct.quantityLock
                }!`,
              )
        }
      }
      // #endregion

      // #region kiểm tra đủ số lượng
      {
        const findInvalidQuantity = productCombo.lstProductInProductCombo.find((pipc: any) => pipc.quantity != 0)
        if (findInvalidQuantity) throw new Error(`Sản phẩm [ ${dicProductInComboId[findInvalidQuantity.productInComboId].code} ] chưa đủ số lượng!`)
      }

      // #endregion
    }

    // #endregion

    return await this.repo.manager.transaction(async (trans) => {
      const lstTask: OutboundDetailEntity[] = []
      const repo = trans.getRepository(OutboundEntity)
      const outboundDetailRepo = trans.getRepository(OutboundDetailEntity)
      const productRepo = trans.getRepository(ItemEntity)
      const productDetailRepo = trans.getRepository(ItemDetailEntity)
      const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)
      const minusEmployeeProductQuantityDto: MinusEmployeeProductQuantityDto = { employeeId: check.employeeId, lstProductInfo: [] }
      const productPriceRepo = trans.getRepository(ItemPriceEntity)

      check.status = enumData.OutboundStatus.APPROVED.code
      check.approvedDate = new Date()
      check.approvedBy = user?.id
      await repo.update(check.id, check)

      // Xoá detail
      // await outboundDetailRepo.delete({ outboundId: check.id })

      for (let detail of lstOutboundComboDetail) {
        // const outboundComboDetail = new OutboundDetailEntity()
        // outboundComboDetail.id = uuidv4()
        // outboundComboDetail.outboundId = check.id
        // outboundComboDetail.productId = detail.productId
        // outboundComboDetail.isCombo = true
        // // outboundComboDetail.productDetailId = detail.productDetailId
        // outboundComboDetail.expiryDate = new Date(detail.expiryDate)
        // outboundComboDetail.manufactureDate = detail?.manufactureDate ? new Date(detail.manufactureDate) : null
        // // outboundComboDetail.lotNumber = detail.lotNumber
        // // outboundComboDetail.inventory = detail.inventory
        // outboundComboDetail.quantity = detail.quantity
        // outboundComboDetail.createdBy = user?.id
        // lstTask.push(outboundComboDetail)

        detail.costPrice = 0

        for (let prod of detail.lstOutboundDetail) {
          // detail.costPrice += dicProductDetail[prod.productDetailId].sellPrice
          // const outboundDetail = new OutboundDetailEntity()
          // outboundDetail.outboundId = check.id
          // outboundDetail.outboundDetailComboId = outboundComboDetail.id
          // outboundDetail.productId = prod.productId
          // outboundDetail.productDetailId = prod.productDetailId
          // outboundDetail.expiryDate = new Date(prod.expiryDate)
          // outboundDetail.manufactureDate = prod?.manufactureDate ? new Date(prod.manufactureDate) : null
          // outboundDetail.lotNumber = prod.lotNumber
          // outboundDetail.inventory = prod.inventory
          // outboundDetail.quantity = prod.quantity
          // outboundDetail.createdBy = user?.id
          // lstTask.push(outboundDetail)

          detail.costPrice += (+dicProductInComboPrice[prod.productId]?.priceCapital || 0) * +prod.quantity

          // #region Trừ tồn kho sp con trong combo
          {
            const productDetail = dicProductDetail[prod.productDetailId]
            const product = dicProduct[prod.productId]
            const warehouseProduct = dicWarehouseProduct[check.warehouseId + prod.productId]
            const warehouseProductDetail = dicWarehouseProductDetail[check.warehouseId + prod.productId + productDetail.id]

            // #region Trừ bảng product và productDetail
            // Trừ của productDetail
            productDetail.quantity = +productDetail.quantity - +prod.quantity
            if (productDetail.quantityLockEmp > 0 || productDetail.quantityLockEmp - +prod.quantity > 0) {
              productDetail.quantityLockEmp = +productDetail.quantityLockEmp - +prod.quantity
            }

            productDetail.updatedAt = new Date()
            productDetail.updatedBy = user?.id
            await productDetailRepo.update(productDetail.id, productDetail)

            // Trừ của product
            product.quantity = +product.quantity - +prod.quantity

            if (product.quantityLockEmp > 0 || product.quantityLockEmp - +prod.quantity > 0) {
              product.quantityLockEmp = +product.quantityLockEmp - +prod.quantity
            }

            product.updatedAt = new Date()
            product.updatedBy = user?.id
            await productRepo.update(product.id, product)
            // #endregion

            // #region Trừ bảng warehouseProduct và warehouseProductDetail
            // Trừ của warehouseProductDetail
            warehouseProductDetail.quantity = +warehouseProductDetail.quantity - +prod.quantity
            warehouseProductDetail.updatedAt = new Date()
            warehouseProductDetail.updatedBy = user?.id
            await warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail)

            // Trừ của warehouseProduct
            warehouseProduct.quantity = +warehouseProduct.quantity - +prod.quantity
            warehouseProduct.updatedAt = new Date()
            warehouseProduct.updatedBy = user?.id
            await warehouseProductRepo.update(warehouseProduct.id, warehouseProduct)
            // #endregion

            minusEmployeeProductQuantityDto.lstProductInfo.push({
              productId: prod.productId,
              productDetailId: prod.productDetailId,
              quantity: prod.quantity,
            })
          }
          // #endregion
        }

        // #region Cộng tồn kho vật lý (Bảng product và productDetail) cho sp combo
        {
          const productCombo = dicProduct[detail.productId]
          if (!productCombo) throw new Error(`Không tìm thấy sản phẩm combo hoặc sản phẩm đã bị ngưng hoạt động!`)
          let productComboDetail: any = dicProductDetail[detail.productId + detail.productDetailId]

          // #region Cộng bảng product và productDetail

          // Cộng của productDetail
          if (productComboDetail) {
            productComboDetail.quantity = +productComboDetail.quantity + +detail.quantity
            productComboDetail.quantityLockEmp = +productComboDetail.quantityLockEmp + +detail.quantity
            productComboDetail.updatedAt = new Date()
            productComboDetail.updatedBy = user?.id
            await productDetailRepo.update(productComboDetail.id, productComboDetail)
          } else {
            productComboDetail = new ItemDetailEntity()
            productComboDetail.id = uuidv4()
            productComboDetail.itemId = detail.productId
            productComboDetail.quantity = detail.quantity
            productComboDetail.manufactureDate = detail.manufactureDate
            productComboDetail.expiryDate = detail.expiryDate
            productComboDetail.costPrice = +detail.costPrice
            productComboDetail.quantityLockEmp = detail.quantity
            productComboDetail.createdAt = new Date()
            productComboDetail.createdBy = user?.id
            await productDetailRepo.insert(productComboDetail)
          }

          // Cộng của product
          productCombo.quantity = +productCombo.quantity + +detail.quantity
          productCombo.quantityLockEmp = +productCombo.quantityLockEmp + +detail.quantity
          productCombo.updatedAt = new Date()
          productCombo.updatedBy = user?.id
          await productRepo.update(productCombo.id, productCombo)
          // #endregion

          let warehouseProduct: any = dicWarehouseProduct[check.warehouseId + detail.productId]
          if (!warehouseProduct) {
            warehouseProduct = new WarehouseProductEntity()
            warehouseProduct.id = uuidv4()
            warehouseProduct.warehouseId = check.warehouseId
            warehouseProduct.productId = detail.productId
            warehouseProduct.productCode = dicProduct[detail.productId]?.code
            warehouseProduct.productName = dicProduct[detail.productId]?.name
            warehouseProduct.quantity = detail.quantity
            warehouseProduct.createdAt = new Date()
            warehouseProduct.createdBy = user?.id
            await warehouseProductRepo.insert(warehouseProduct)
          } else {
            const quantityOld = +warehouseProduct.quantity
            const quantityNew = quantityOld + +detail.quantity
            warehouseProduct.quantity = quantityNew
            await warehouseProductRepo.update(warehouseProduct.id, warehouseProduct)
          }

          let warehouseProductDetail: any =
            dicWarehouseProductDetail[check.warehouseId + detail.productId + +moment(detail.expiryDate).format('YYYY-MM-DD')]
          if (!warehouseProductDetail) {
            const warehouseProductDetail = new WarehouseProductDetailEntity()
            warehouseProductDetail.id = uuidv4()
            warehouseProductDetail.warehouseId = check.warehouseId
            warehouseProductDetail.productId = detail.productId
            warehouseProductDetail.productCode = dicProduct[detail.productId]?.code
            warehouseProductDetail.productName = dicProduct[detail.productId]?.name
            warehouseProductDetail.productDetailId = productComboDetail.id
            warehouseProductDetail.warehouseProductId = warehouseProduct.id
            warehouseProductDetail.quantity = detail.quantity
            warehouseProductDetail.manufactureDate = detail?.manufactureDate || null
            warehouseProductDetail.expiryDate = detail.expiryDate
            warehouseProductDetail.createdAt = new Date()
            warehouseProductDetail.createdBy = user?.id
            await warehouseProductDetailRepo.insert(warehouseProductDetail)
          } else {
            const quantityDetailNew = +warehouseProductDetail.quantity + +detail.quantity
            warehouseProductDetail.quantity = quantityDetailNew
            warehouseProductDetail = await warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail)
          }

          const productPriceObj = await productPriceRepo.findOne({
            where: { itemId: productCombo.id, isFinal: true },
            order: { createdAt: 'DESC' },
          })
          if (productPriceObj) {
            await productPriceRepo.update(
              {
                itemId: productCombo.id,
                id: productPriceObj.id,
              },
              {
                priceCapital: +detail.costPrice,
              },
            )
          }
        }
        // #endregion
      }
      // #endregion

      // Trừ tồn kho nhân viên
      // await authApiHelper.minusEmployeeProductQuantity(req, minusEmployeeProductQuantityDto)

      await outboundDetailRepo.insert(lstTask)
      let description: string = `Nhân viên ${user?.username} duyệt phiếu xuất kho tạo combo`

      await this.createHistory({ outboundId: check.id, description }, trans)

      // #region Tạo phiếu nhập kho cho sản phẩm combo

      let lstDetail: any[] = []

      for (let wtd of lstOutboundComboDetail) {
        lstDetail.push({
          productId: wtd.productId,
          productName: dicProductCombo[wtd.productId].name,
          productCode: dicProductCombo[wtd.productId].code,
          unitId: null,
          expiryDate: wtd.expiryDate,
          manufactureDate: wtd.manufactureDate,
          lotNumber: null,
          inventory: null,
          quantity: wtd.quantity,
          costPrice: wtd.costPrice,
        })
      }

      const inboundCreateDto: any = {
        type: enumData.InboundType.COMBO.code,
        warehouseId: check.warehouseId,
        lstDetail: lstDetail,
      }

      await this.inboundService.createDataApproved(inboundCreateDto, req, trans)
      // #endregion

      return { message: `Duyệt phiếu xuất kho tạo combo có mã [ ${check.code} ] thành công` }
    })
  }

  async checkRemain(data: CheckingRemainDto[], req: IRequest, user?: UserDto) {
    /* Tìm ra danh sách kho  */
  }

  /** Hàm duyệt phiếu xuất kho */
  async updateApprove(data: FilterOneDto, req: IRequest) {
    try {
      const entity: OutboundEntityExtend = await this.repo.findOne({
        where: { id: data.id },
        relations: { outboundDetails: true },
      })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
      if (entity.status != enumData.OutboundStatus.NEW.code)
        throw new Error(`Chỉ có thể duyệt phiếu ở trạng thái [ ${enumData.OutboundStatus.NEW.name} ]`)

      if (entity.orderId) throw new Error(`Không thể duyệt phiếu xuất kho được tạo từ đơn hàng!`)

      const lstOutboundDetail = entity.__outboundDetails__

      // Danh sách hạn sử dụng đã format dạng YYYY-MM-DD
      let lstProductDetailId: string[] = lstOutboundDetail.map((wtd) => wtd.productDetailId)

      // Danh sách id sản phẩm
      const lstProductId = lstOutboundDetail.mapAndDistinct((wtd) => wtd.productId)

      // Dic Product
      let dicProduct: any = {}
      {
        const lstProduct = await this.productRepo.find({
          where: { id: In(lstProductId), isDeleted: false },
          select: { id: true, code: true, name: true, quantity: true, quantityLock: true, quantityLockEmp: true },
        })
        dicProduct = coreHelper.arrayToObject(lstProduct)
      }

      const dicProductDetail: any = {}
      {
        const lstProductDetail: ItemDetailEntity[] = await this.productDetailRepo.find({
          where: {
            id: In(lstProductDetailId),
          },
          select: { id: true, itemId: true, expiryDate: true, quantity: true, quantityLock: true, quantityLockEmp: true },
        })

        if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

        // Số lượng: quantity
        // Số lượng đã lên đơn tạm: quantityLock
        // Số lượng lock khi phân kho: quantityLockEmp

        for (let pd of lstProductDetail) {
          // Tạo dic dicProductDetail
          dicProductDetail[pd.id] = pd
        }
      }

      let dicWarehouseProduct: any = {}
      let dicWarehouseProductDetail: any = {}
      {
        const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
          where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: entity.warehouseId, isDeleted: false },
          select: {
            id: true,
            warehouseProductId: true,
            warehouseId: true,
            productId: true,
            productDetailId: true,
            expiryDate: true,
            quantity: true,
            quantityLock: true,
            quantityExport: true,
          },
        })
        let lstWarehouseProductId: string[] = []
        for (let wpd of lstWarehouseProductDetail) {
          dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
          lstWarehouseProductId.push(wpd.warehouseProductId)
        }

        const lstWarehouseProduct = await this.warehouseProductRepo.find({
          where: { id: In(lstWarehouseProductId), isDeleted: false },
          select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true, quantityExport: true },
        })

        for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
      }

      // Kiểm tra có đủ tồn kho không
      for (let obd of lstOutboundDetail) {
        const productDetail = dicProductDetail[obd.productDetailId]
        if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

        const product = dicProduct[obd.productId]
        if (!product) throw new Error(`Không tìm thấy sản phẩm thực`)

        const warehouseProduct = dicWarehouseProduct[entity.warehouseId + obd.productId]
        if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho vật lý`)

        const warehouseProductDetail = dicWarehouseProductDetail[entity.warehouseId + obd.productId + productDetail.id]
        if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho vật lý`)

        // const employeeeProduct = dicEmployeeProduct[entity.employeeId + obd.productId]
        // if (!employeeeProduct) throw new Error(`Không tìm thấy sản phẩm trong kho nhân viên`)

        // const employeeeProductDetail = dicEmployeeProductDetail[entity.employeeId + obd.productId + productDetail.id]
        // if (!employeeeProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho nhân viên`)

        // #region kiểm tra điều kiện trước khi trừ tồn kho
        {
          if (obd.quantity > +productDetail.quantity - +productDetail.quantityLock)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                +productDetail.quantity - +productDetail.quantityLock
              }!`,
            )

          if (obd.quantity > +product.quantity - +product.quantityLock)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${+product.quantity - +product.quantityLock}!`,
            )

          if (obd.quantity > +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock
              }!`,
            )

          if (obd.quantity > +warehouseProduct.quantity - +warehouseProduct.quantityLock)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                +warehouseProduct.quantity - +warehouseProduct.quantityLock
              }!`,
            )
        }
      }

      await this.repo.manager.transaction(async (trans) => {
        const repo = trans.getRepository(OutboundEntity)
        const productRepo = trans.getRepository(ItemEntity)
        const productDetailRepo = trans.getRepository(ItemDetailEntity)
        const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
        const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)
        const productInventoryHistoryRepo = trans.getRepository(ProductInventoryHistoryEntity)
        const minusEmployeeProductQuantityDto: MinusEmployeeProductQuantityDto = { employeeId: entity.employeeId, lstProductInfo: [] }
        const quantityNew = coreHelper.selectSum(lstOutboundDetail, 'quantity')
        for (let obd of lstOutboundDetail) {
          const productDetail = dicProductDetail[obd.productDetailId]
          const product = dicProduct[obd.productId]
          const warehouseProduct = dicWarehouseProduct[entity.warehouseId + obd.productId]
          const warehouseProductDetail = dicWarehouseProductDetail[entity.warehouseId + obd.productId + productDetail.id]

          // #region Trừ bảng product và productDetail
          // Trừ của productDetail
          productDetail.quantity = +productDetail.quantity - +obd.quantity
          productDetail.quantityLockEmp = +productDetail.quantityLockEmp - +obd.quantity
          productDetail.updatedAt = new Date()
          productDetail.updatedBy = data.approveBy
          await productDetailRepo.update(productDetail.id, productDetail)

          // Trừ của product
          product.quantity = +product.quantity - +obd.quantity
          product.quantityLockEmp = +product.quantityLockEmp - +obd.quantity
          product.updatedAt = new Date()
          product.updatedBy = data.approveBy
          await productRepo.update(product.id, product)

          // #endregion
          let quantityToAdd = Number(obd.quantity) || 0

          // #region Trừ bảng warehouseProduct và warehouseProductDetail
          // Trừ của warehouseProductDetail
          warehouseProductDetail.quantity = Number(warehouseProductDetail.quantity) - Number(obd.quantity)
          warehouseProductDetail.quantityExport += Number(quantityToAdd)
          warehouseProductDetail.updatedAt = new Date()
          warehouseProductDetail.updatedBy = data.approveBy
          await warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail)

          // Trừ của warehouseProduct
          warehouseProduct.quantity = +warehouseProduct.quantity - +obd.quantity
          warehouseProduct.quantityExport += quantityToAdd
          warehouseProduct.updatedAt = new Date()
          warehouseProduct.updatedBy = data.approveBy
          await warehouseProductRepo.update(warehouseProduct.id, warehouseProduct)
          // #endregion

          let quantityOld: any = 0
          let lstOldWh = await warehouseProductRepo.find({ where: { warehouseId: warehouseProduct.warehouseId, productId: product.id } })
          if (lstOldWh.length > 0) {
            quantityOld = coreHelper.selectSum(lstOldWh, 'quantity')
          }

          // lưu lịch sử
          const his = new ProductInventoryHistoryEntity()
          his.warehouseId = warehouseProduct.warehouseId
          his.warehouseProductId = warehouseProduct.id
          his.warehouseProductDetailId = warehouseProductDetail.id
          his.productId = obd.productId
          his.outboundId = obd.outboundId
          his.outboundDetailId = obd.id
          his.quantity = quantityOld
          his.quantityNew = quantityNew
          his.description = `Duyệt PXK [${entity.code}]<br>`
          his.createdAt = new Date()
          his.createdBy = data.approveBy
          await productInventoryHistoryRepo.insert(his)

          minusEmployeeProductQuantityDto.lstProductInfo.push({
            productId: obd.productId,
            productDetailId: obd.productDetailId,
            quantity: obd.quantity,
          })

          // await authApiHelper.minusEmployeeProductQuantity(req, minusEmployeeProductQuantityDto)
        }

        // Xoá relation khi update
        delete entity.__outboundDetails__

        entity.status = enumData.OutboundStatus.APPROVED.code
        entity.approvedBy = data.approveBy
        entity.approvedDate = new Date()
        await repo.update(entity.id, entity)

        const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

        // Tạo lịch sử
        const description = `Nhân viên ${member[0]?.fullName} duyệt phiếu xuất kho, chuyển trạng thái của phiếu từ ${enumData.OutboundStatus.NEW.name} thành ${enumData.OutboundStatus.APPROVED.name}`
        await this.createHistory({ outboundId: entity.id, description }, trans)
      })
      return { message: 'Duyệt phiếu xuất kho thành công!' }
    } catch (error) {
      throw new Error(error)
    }
  }

  /** Hàm xác nhận soạn hàng cho phiếu xuất kho có orderId */
  async updateConfirmPreparation(data: FilterOneDto, user?: UserDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    if (!entity.orderId) throw new Error(`Phiếu xuất kho không có đơn hàng!`)

    entity.status = enumData.OutboundStatus.PREPARED.code
    entity.preparedAt = new Date()
    entity.preparedBy = user?.id

    const description = `Nhân viên ${user?.username} xác nhận soạn hàng cho phiếu xuất kho`
    await this.createHistory({ outboundId: entity.id, description }, this.repo.manager)

    return { message: 'Xác nhận soạn hàng cho phiếu xuất kho thành công!' }
  }

  /** Hàm huỷ phiếu xuất kho */
  async updateCancel(data: FilterOneDto, req: IRequest) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status != enumData.OutboundStatus.NEW.code)
      throw new Error(`Chỉ có thể huỷ phiếu ở trạng thái [ ${enumData.OutboundStatus.NEW.name} ]`)

    entity.status = enumData.OutboundStatus.CANCEL.code
    entity.updatedBy = data.approveBy
    await this.repo.update(entity.id, entity)

    const member: any = await omsApiHelper.getMemberByListId(req, [data.approveBy])

    const description = `Nhân viên ${member[0]?.fullName} huỷ phiếu xuất kho`
    await this.createHistory({ outboundId: entity.id, description }, this.repo.manager)

    return { message: 'Huỷ phiếu xuất kho thành công!' }
  }

  /** Hàm tạo mới phiếu xuất kho đã duyệt */
  async createDataApproved(data: OutboundCreateDto, req?: IRequest, manager?: EntityManager) {
    const checkWarehouse = await this.warehouseRepo.findOne({ where: { id: data.warehouseId, isDeleted: false }, select: { id: true } })
    if (!checkWarehouse) throw new Error(`Không tìm thấy kho vật lý hoặc kho vật lý đã bị ngưng hoạt động!`)

    // #region Check tồn kho

    // Danh sách id sản phẩm
    const lstProductId = data.lstOutboundDetail.mapAndDistinct((wtd) => wtd.productId)

    // Dic Product
    let dicProduct: any = {}
    {
      const lstProduct = await this.productRepo.find({
        where: { id: In(lstProductId) },
        select: { id: true, code: true, name: true, quantity: true, quantityLock: true, quantityLockEmp: true },
      })
      dicProduct = coreHelper.arrayToObject(lstProduct)
    }
    let lstProductDetailId: string[] = data.lstOutboundDetail.map((wtd) => wtd.productDetailId)
    const dicProductDetail: any = {}

    {
      let lstProductDetail = await this.productDetailRepo.find({
        where: {
          id: In(lstProductDetailId),
        },
        select: { id: true, itemId: true, expiryDate: true, quantity: true, quantityLock: true, quantityLockEmp: true, lotNumber: true },
      })
      if (lstProductDetail.length == 0) throw new Error(`Không tìm thấy danh sách sản phẩm trong kho`)

      // Số lượng: quantity
      // Số lượng đã lên đơn tạm: quantityLock
      // Số lượng lock khi phân kho: quantityLockEmp

      for (let pd of lstProductDetail) {
        dicProductDetail[pd.id] = pd
      }
    }

    let dicWarehouseProduct: any = {}
    let dicWarehouseProductDetail: any = {}
    {
      const lstWarehouseProductDetail = await this.warehouseProductDetailRepo.find({
        where: { productDetailId: In(lstProductDetailId), productId: In(lstProductId), warehouseId: data.warehouseId, isDeleted: false },
        select: {
          id: true,
          warehouseProductId: true,
          warehouseId: true,
          productId: true,
          productDetailId: true,
          expiryDate: true,
          quantity: true,
          quantityLock: true,
          quantityExport: true,
        },
      })
      let lstWarehouseProductId: string[] = []
      for (let wpd of lstWarehouseProductDetail) {
        dicWarehouseProductDetail[wpd.warehouseId + wpd.productId + wpd.productDetailId] = wpd
        lstWarehouseProductId.push(wpd.warehouseProductId)
      }

      const lstWarehouseProduct = await this.warehouseProductRepo.find({
        where: { id: In(lstWarehouseProductId), isDeleted: false },
        select: { id: true, warehouseId: true, productId: true, quantity: true, quantityLock: true, quantityExport: true, quantityBegin: true },
      })

      for (let wp of lstWarehouseProduct) dicWarehouseProduct[wp.warehouseId + wp.productId] = wp
    }

    for (let obd of data.lstOutboundDetail) {
      let productDetail = dicProductDetail[obd.productDetailId]
      if (!productDetail) productDetail = dicProductDetail[obd.productDetailId]
      if (!productDetail) throw new Error(`Không tìm thấy sản phẩm trong kho`)

      const product = dicProduct[obd.productId]
      if (!product) throw new Error(`Không tìm thấy sản phẩm thực`)

      const warehouseProduct = dicWarehouseProduct[data.warehouseId + obd.productId]
      if (!warehouseProduct) throw new Error(`Không tìm thấy sản phẩm trong kho vật lý`)

      const warehouseProductDetail = dicWarehouseProductDetail[data.warehouseId + obd.productId + productDetail.id]
      if (!warehouseProductDetail) throw new Error(`Không tìm thấy chi tiết sản phẩm trong kho vật lý`)

      // #region kiểm tra điều kiện trước khi trừ tồn kho
      {
        if (obd.quantity > +productDetail.quantity - +productDetail.quantityLock)
          if (!productDetail)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                +productDetail.quantity - +productDetail.quantityLock
              }!`,
            )

        if (obd.quantity > +product.quantity - +product.quantityLock)
          if (!product)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${+product.quantity - +product.quantityLock}!`,
            )

        if (obd.quantity > +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock)
          if (!warehouseProductDetail)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                +warehouseProductDetail.quantity - +warehouseProductDetail.quantityLock
              }!`,
            )

        if (obd.quantity > +warehouseProduct.quantity - +warehouseProduct.quantityLock)
          if (!warehouseProduct)
            throw new Error(
              `số lượng xuất của sản phẩm ${product.name} ${obd.quantity} lớn hơn số lượng tồn kho ${
                +warehouseProduct.quantity - +warehouseProduct.quantityLock
              }!`,
            )
      }
    }

    // #endregion

    // Sinh code theo quy tắc ddmmyyy_xxxx (xxxx tăng dần) VD: 01102023_0001
    const curDate = moment(new Date()).format('DDMMYYYY')

    let genCode: string
    // Đếm số lượng phiếu xuất kho trong ngày
    let count = await this.repo.count({ where: { code: Like(`${curDate}_%`) }, select: { id: true } })
    if (!count) count = 0

    genCode = this.genCode(count)
    let entityManager: EntityManager
    if (!manager) entityManager = this.repo.manager
    else entityManager = manager

    return await entityManager.transaction(async (trans) => {
      const lstTask: OutboundDetailEntity[] = []
      const repo = trans.getRepository(OutboundEntity)
      const outboundDetailRepo = trans.getRepository(OutboundDetailEntity)
      const productRepo = trans.getRepository(ItemEntity)
      const productDetailRepo = trans.getRepository(ItemDetailEntity)
      const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)
      const productInventoryHistoryRepo = trans.getRepository(ProductInventoryHistoryEntity)

      const outbound = new OutboundEntity()
      outbound.id = uuidv4()
      outbound.code = genCode
      outbound.type = data.type
      outbound.warehouseId = data.warehouseId
      outbound.employeeId = data?.employeeId ?? null
      outbound.createdAt = new Date(data.createdAt)
      outbound.warehouseTransferId = data?.warehouseTransferId ?? null
      outbound.checkInventoryId = data?.checkInventoryId ?? null
      outbound.status = enumData.OutboundStatus.APPROVED.code
      outbound.approvedBy = data.createBy
      outbound.approvedDate = new Date()
      outbound.createdBy = data.createBy
      outbound.description = data?.description ?? null
      await repo.insert(outbound)

      for (let detail of data.lstOutboundDetail) {
        const outboundDetail = new OutboundDetailEntity()
        outboundDetail.outboundId = outbound.id
        outboundDetail.productId = detail.productId
        outboundDetail.productDetailId = detail.productDetailId
        outboundDetail.expiryDate = new Date(detail.expiryDate)
        outboundDetail.manufactureDate = detail?.manufactureDate ? new Date(detail.manufactureDate) : null
        outboundDetail.lotNumber = detail.lotNumber
        outboundDetail.inventory = detail.inventory
        outboundDetail.quantity = detail.quantity
        outboundDetail.createdBy = data.createBy
        lstTask.push(outboundDetail)
      }

      if (outbound?.employeeId) {
        const minusEmployeeProductQuantityDto: MinusEmployeeProductQuantityDto = { employeeId: outbound.employeeId, lstProductInfo: [] }
        for (let obd of data.lstOutboundDetail) {
          minusEmployeeProductQuantityDto.lstProductInfo.push({
            productId: obd.productId,
            productDetailId: obd.productDetailId,
            quantity: obd.quantity,
          })
        }
        // await authApiHelper.minusEmployeeProductQuantity(req, minusEmployeeProductQuantityDto)
      }

      const quantityNew = coreHelper.selectSum(data.lstOutboundDetail, 'quantity')
      for (let obd of lstTask) {
        const productDetail = dicProductDetail[obd.productDetailId]
        const product = dicProduct[obd.productId]
        const warehouseProduct = dicWarehouseProduct[outbound.warehouseId + obd.productId]
        const warehouseProductDetail = dicWarehouseProductDetail[outbound.warehouseId + obd.productId + productDetail.id]

        // #region Trừ bảng product và productDetail
        // Trừ của productDetail
        productDetail.quantity = +productDetail.quantity - +obd.quantity
        productDetail.quantityLockEmp = +productDetail.quantityLockEmp - +obd.quantity
        productDetail.updatedAt = new Date()
        productDetail.updatedBy = outbound.approvedBy
        await productDetailRepo.update(productDetail.id, productDetail)

        // Trừ của product
        product.quantity = +product.quantity - +obd.quantity
        product.quantityLockEmp = +product.quantityLockEmp - +obd.quantity
        product.updatedAt = new Date()
        product.updatedBy = outbound.approvedBy
        await productRepo.update(product.id, product)

        // #endregion
        let quantityToAdd = Number(obd.quantity) || 0

        // #region Trừ bảng warehouseProduct và warehouseProductDetail
        // Trừ của warehouseProductDetail
        warehouseProductDetail.quantity = Number(warehouseProductDetail.quantity) - Number(obd.quantity)
        warehouseProductDetail.quantityExport += Number(quantityToAdd)
        warehouseProductDetail.updatedAt = new Date()
        warehouseProductDetail.updatedBy = outbound.approvedBy
        await warehouseProductDetailRepo.update(warehouseProductDetail.id, warehouseProductDetail)

        // Trừ của warehouseProduct
        warehouseProduct.quantity = +warehouseProduct.quantity - +obd.quantity
        warehouseProduct.quantityExport += Number(quantityToAdd)
        warehouseProduct.updatedAt = new Date()
        warehouseProduct.updatedBy = outbound.approvedBy
        await warehouseProductRepo.update(warehouseProduct.id, warehouseProduct)
        // #endregion

        let quantityOld: any = 0
        let lstOldWh = await warehouseProductRepo.find({ where: { warehouseId: warehouseProduct.warehouseId, productId: product.id } })
        if (lstOldWh.length > 0) {
          quantityOld = coreHelper.selectSum(lstOldWh, 'quantity')
        }

        // lưu lịch sử
        const his = new ProductInventoryHistoryEntity()
        his.warehouseId = warehouseProduct.warehouseId
        his.warehouseProductId = warehouseProduct.id
        his.warehouseProductDetailId = warehouseProductDetail.id
        his.productId = obd.productId
        his.outboundId = outbound.id
        his.outboundDetailId = obd.id
        his.quantity = quantityOld
        his.quantityNew = quantityNew
        his.description = `Duyệt PXK [${outbound.code}]<br>`
        his.createdAt = new Date()
        his.createdBy = outbound.approvedBy
        await productInventoryHistoryRepo.insert(his)
      }

      const member: any = await omsApiHelper.getMemberByListId(req, [data.createBy])

      await outboundDetailRepo.insert(lstTask)
      let description: string
      if (data.orderId) description = `Nhân viên ${member[0]?.username} tạo mới phiếu xuất kho từ đơn hàng`
      else description = `Nhân viên ${member[0]?.username} tạo mới phiếu xuất kho`

      await this.createHistory({ outboundId: outbound.id, description }, trans)

      return { message: `Tạo mới phiếu xuất kho có mã {${outbound.code}} thành công` }
    })
  }
}
