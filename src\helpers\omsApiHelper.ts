import * as dotenv from 'dotenv'
import { Request as IRequest } from 'express'
import { CreatePartnerDto, CreateStoreDto, DeclareCardDuration, DeclareCardPeriod, DeclareCardType } from './dto/apiCaller.dto'
import { callApiHelper } from './callApiHelper'
import { FilterOneDto, PaginationDto } from '../dto'
import { MemberPaginationDto } from '../modules/core/member/dto/memberPagination.dto'
import { WithdrawRequestReq } from '../modules/core/withdrawRequest/dto/withdrawRequestReq.dto'
import { UUIDReq } from '../dto/id.dto'
import { UpdateWithdrawRequestReq } from '../modules/core/withdrawRequest/dto/updateWithdrawRequestReq.dto'
dotenv.config()

class OmsApiHelper {
  private url = process.env.HOST_OMS_SETTING

  async createStore(req: IRequest, data: CreateStoreDto): Promise<any> {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/create-store`)
  }

  async paginationStore(req: IRequest, data: PaginationDto): Promise<any> {
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/list-store?pageSize=${data.skip}&pageIndex=${data.take}`)
  }

  async createPartner(req: IRequest, data: CreatePartnerDto): Promise<any> {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/create`)
  }

  async lstPartner(req: IRequest, data: any): Promise<any> {
    return await callApiHelper.processCallGetApiHelper(
      req,
      `${this.url}/api/admin/partner/list-partner?pageSize=${data.pageSize}&pageIndex=${data.pageIndex}`,
    )
  }

  async lstPartnerLv1(req: IRequest): Promise<any> {
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/list-partner-lv1`)
  }

  async lstPartnerByCodes(req: IRequest, data: any): Promise<any> {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/list-by-codes`)
  }

  async getMember(req: IRequest, data: MemberPaginationDto): Promise<any> {
    const query = this.objToQueryString(data)
    const newUrl = `${this.url}/api/admin/member/list?${query}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async getCustomer(req: IRequest, data: MemberPaginationDto): Promise<any> {
    const query = this.objToQueryString(data)
    const newUrl = `${this.url}/api/admin/member/list-customer?${query}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async getMemberByListId(req: IRequest, listId: string[]): Promise<any> {
    return await callApiHelper.processCallApiHelper(req, listId, `${this.url}/api/members/find`)
  }

  async getMemberDetail(req: IRequest, data: FilterOneDto): Promise<any> {
    const query = this.objToQueryString(data)
    const newUrl = `${this.url}/api/admin/find-detail?${query}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async getCardPeriod(req: IRequest, data: { pageSize: any; pageIndex: any }): Promise<any> {
    return await callApiHelper.processCallGetApiHelper(
      req,
      `${this.url}/api/admin/config-card/period?pageSize=${data.pageSize}&pageIndex=${data.pageIndex}`,
    )
  }

  async getCardDuration(req: IRequest, data: { pageSize: any; pageIndex: any }): Promise<any> {
    return await callApiHelper.processCallGetApiHelper(
      req,
      `${this.url}/api/admin/config-card/duration?pageSize=${data.pageSize}&pageIndex=${data.pageIndex}`,
    )
  }

  async getCard(req: IRequest, data: { pageSize: any; pageIndex: any }): Promise<any> {
    return await callApiHelper.processCallGetApiHelper(
      req,
      `${this.url}/api/members/cards/list?pageSize=${data.pageSize}&pageIndex=${data.pageIndex}`,
    )
  }

  async getCardType(req: IRequest, data: { pageSize: any; pageIndex: any }): Promise<any> {
    return await callApiHelper.processCallGetApiHelper(
      req,
      `${this.url}/api/admin/config-card/type?pageSize=${data.pageSize}&pageIndex=${data.pageIndex}`,
    )
  }

  async importCardPeriodExcel(req: IRequest, data: DeclareCardPeriod[]) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/config-card/import-period-excel`)
  }

  async importCardDurationExcel(req: IRequest, data: DeclareCardDuration[]) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/config-card/import-duration-excel`)
  }

  async importCardTypeExcel(req: IRequest, data: DeclareCardType[]) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/config-card/import-type-excel`)
  }

  async listWithdraw(req: IRequest, data: WithdrawRequestReq) {
    const query = this.objToQueryString(data)
    const newUrl = `${this.url}/api/members/withdraw-request/list?${query}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async updateWithdraw(req: IRequest, data: UpdateWithdrawRequestReq) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/members/withdraw-request/update`)
  }

  async detailWithdraw(req: IRequest, data: UUIDReq) {
    const query = this.objToQueryString(data)
    const newUrl = `${this.url}/api/members/withdraw-request/detail?${query}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async updateSOStatus(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/update-so-status`)
  }

  async getOrderByIds(req?: IRequest, data?: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/list-order`)
  }

  async getDetailOrder(req?: IRequest, data?: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/detail-order`)
  }

  async getStatisticalOverview(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    const newUrl = `${this.url}/api/admin/member/statistical/overview?${query}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async getStatisticalDetails(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/member/statistical/details`)
  }

  async listBusinesstypeByMember(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    const newUrl = `${this.url}/api/admin/member/list-businesstype-by-member?${query}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async findListProductCombo(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/ape/item/find_list_product_combo`)
  }

  async listCardsByMember(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    const newUrl = `${this.url}/api/admin/list-cards-by-member?${query}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async listItemGroup(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    const newUrl = `${this.url}/api/publics/product/list-item-group?${query}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async sendNotificationOrder(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/send-noti-so`)
  }
  async listCard(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/cards?${query}`)
  }
  async cardSalesHistory(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/payments/card-sales-history?${query}`)
  }
  async listPartner(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/list-partner?${query}`)
  }

  async listProductPeriodBonus(req: IRequest) {
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/period-config-bonus/list-all?pageSize=99999&pageIndex=1`)
  }

  //#region PERIOD_BONUS
  async getPeriodBonusList(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/period-config-bonus/list`)
  }

  async createPeriodBonus(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/period-config-bonus/create`)
  }

  async updatePeriodBonus(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/period-config-bonus/update`)
  }

  async inActivePeriodBonus(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/period-config-bonus/in-active`)
  }

  async deletePeriodBonus(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/period-config-bonus/delete`)
  }
  //#endregion

  //#region PARTNER_BL
  async getPartnerLv1List(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/list-partner-lv1?${query}`)
  }
  //#endregion

  //#region PARTNER
  async getPartnerList(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/list-partner?${query}`)
  }

  async getPartnerCommission(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/get-partner-commission?${query}`)
  }

  async importPartnerConfig(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/import-partner-config`)
  }

  async getPartnerSettingCommission(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/get-partner-setting-commission?${query}`)
  }

  async updatePartnerSettingCommission(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/update-partner-setting-commission`)
  }

  async updateBonusConfig(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/update-bonus-config`)
  }

  async getMemberPartnerConfig(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner-config/get-member-partner-config?${query}`)
  }

  async updateStatusPartner(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/partner/update-status-partner`)
  }

  async importPartnerConfigAgain(req: IRequest, file: Express.Multer.File) {
    return await callApiHelper.processCallApiHelperWithFile(req, file, `${this.url}/api/admin/partner-config/import-partner-config-again`)
  }

  async getPartnerDeposit(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/get-partner-deposit?${query}`)
  }

  async createPartnerDeposit(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/create-partner-deposit`)
  }

  async updatePartnerDeposit(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/partner/update-partner-deposit`)
  }

  async updateInfoPartner(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/update-info-partner`)
  }
  //#endregion

  //#region WITHDRAWALS
  async getWithdrawStatement(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/withdraw-statement?${query}`)
  }

  async getWithdrawRequest(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/withdraw-request?${query}`)
  }

  async rejectWithdrawRequest(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/withdraw-request/reject`)
  }

  async updateWithdrawRequestInfo(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/withdraw-request/update-info`)
  }

  async getWithdrawRequestById(req: IRequest, id: string) {
    const newUrl = `${this.url}/api/admin/withdraw-request/${id}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async reWithdraw(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/withdraw-request/re-withdraw`)
  }

  async updateReWithdraw(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/withdraw-request/update-re-withdraw`)
  }
  //#endregion

  //#region WITHDRAW_PROFILE
  async getWithdrawProfileById(req: IRequest, id: string) {
    const newUrl = `${this.url}/api/admin/withdraw-profile/${id}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }
  async getWithdrawProfile(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/withdraw-profile?${query}`)
  }

  async createWithdrawProfile(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/withdraw-profile/create`)
  }

  async addWithdrawProfileNote(req: IRequest, id: string, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/withdraw-profile/${id}/add-note`)
  }

  async addWithdrawProfileContractNote(req: IRequest, id: string, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/withdraw-profile/${id}/add-contract-note`)
  }

  async getWithdrawStatementById(req: IRequest, id: string) {
    const newUrl = `${this.url}/api/admin/withdraw-statement/${id}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }

  async rejectWithdrawStatement(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/withdraw-statement/reject`)
  }

  async approveWithdrawStatement(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/withdraw-statement/approve`)
  }

  async requestWithdrawStatementApproval(req: IRequest, id: string, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/withdraw-statement/${id}/request-approval`)
  }

  async getWithdrawStatementContract(req: IRequest, id: string) {
    const newUrl = `${this.url}/api/admin/withdraw-statement/get-contract/${id}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }
  //#endregion

  //#region WITHDRAW_TRANSACTION
  async getWithdrawTransactions(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/withdraw-transactions?${query}`)
  }

  async confirmWithdrawTransaction(req: IRequest, id: string, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/withdraw-transactions/${id}/confirm`)
  }

  async rejectWithdrawTransaction(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/withdraw-transactions/reject`)
  }

  async addWithdrawTransactionPaymentNote(req: IRequest, id: string, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/withdraw-transactions/${id}/add-payment-note`)
  }

  async getWithdrawTransactionById(req: IRequest, id: string) {
    const newUrl = `${this.url}/api/admin/withdraw-transactions/${id}`
    return await callApiHelper.processCallGetApiHelper(req, newUrl)
  }
  //#endregion

  //#region STORE
  async getStoreList(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/list-store?${query}`)
  }

  async updateStore(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/partner/update-store`)
  }

  async importStore(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/import-store`)
  }

  async updateStoreStatus(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/partner/update-status-store`)
  }

  async searchStore(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/search-store?${query}`)
  }
  //#endregion

  //#region TRACKING
  async getOrderList(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/list-order`)
  }

  async getOrderDetail(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/detail-order`)
  }

  async updatePendingDeliveryOrder(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/update-pending-delivery-order`)
  }

  async updateDeliveringOrder(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/update-delivering-order`)
  }

  async updateDeliveredOrder(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/update-delivered-order`)
  }

  async cancelOrder(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/cancel-order`)
  }

  async printOrder(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/orders/print-order`)
  }

  //#endregion
  //#region CONFIG_CARD_TYPE
  async getConfigCardType(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/config-card/type?${query}`)
  }

  async searchConfigCardType(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/config-card/card-type/search?${query}`)
  }

  async createConfigCardType(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/config-card/create-type`)
  }

  async updateConfigCardType(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/config-card/update-card-type`)
  }

  async deleteConfigCardType(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/config-card/delete-card-type`)
  }

  //type-by-bussiness
  async getCardTypeByBusiness(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/config-card/type-by-bussiness`)
  }
  //#endregion

  //#region CONFIG_CARD_PERIOD
  async getConfigCardPeriod(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/config-card/period?${query}`)
  }

  async searchConfigCardPeriod(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/config-card/period/search?${query}`)
  }

  async createConfigCardPeriod(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/config-card/create-period`)
  }

  async updateConfigCardPeriod(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/config-card/update-period`)
  }

  async deleteConfigCardPeriod(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/config-card/delete-period`)
  }
  //#endregion

  //#region CONFIG_CARD_DURATION
  async getConfigCardDuration(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/config-card/duration?${query}`)
  }

  async searchConfigCardDuration(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/config-card/duration/search?${query}`)
  }

  async createConfigCardDuration(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/config-card/create-duration`)
  }

  async updateConfigCardDuration(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/config-card/update-duration`)
  }

  async deleteConfigCardDuration(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/config-card/delete-duration`)
  }
  //#endregion

  //#region PARTNER_CARD
  async getPartnerCards(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/cards?${query}`)
  }

  async createPartnerCards(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/create-cards`)
  }

  async updatePartnerCard(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/partner/update-card`)
  }

  async deletePartnerCard(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/delete-card`)
  }

  async searchPartnerCard(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/search-card?${query}`)
  }

  async updatePartnerCardStatus(req: IRequest, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/partner/update-status-card`)
  }
  //#endregion

  //#region COMBO_CARD
  async assignComboCard(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/assign-combo-card`)
  }
  //#endregion

  //#region CARD_SALES_HISTORY
  async getCardSalesHistory(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/payments/card-sales-history`)
  }

  async searchCardSalesHistory(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/payments/card-sales-history-search?${query}`)
  }
  //#endregion

  //#region TRANSACTION
  async getTransactionHistory(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/payments/transaction-history?${query}`)
  }

  async getDetailTransactionHistory(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/payments/detail-transaction-history?${query}`)
  }
  //#endregion

  //#region MEMBER
  async getCardsByMember(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/member/list-cards-by-member?${query}`)
  }

  //update-password
  async updateMemberPassword(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/member/update-password`)
  }

  //#endregion

  //#region STATEMENT
  async getStatementByProduct(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/statement/statement-by-product?${query}`)
  }

  async getStatementByCard(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/statement/statement-by-card?${query}`)
  }

  async getCardStatementByDate(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/statement/card-statement-by-date?${query}`)
  }

  async getProductStatementByDate(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/statement/product-statement-by-date?${query}`)
  }

  async getCardQuantityStatement(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/statement/card-quantity-statement?${query}`)
  }

  async getDepositCardStatementByDate(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/statement/deposit-card-statement-by-date?${query}`)
  }

  async getCardSalesHistoryByDate(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/statement/card-sales-history-by-date`)
  }
  //#endregion

  //#region RECONCILIATIONS
  async createReconciliation(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/partner/reconciliations/create`)
  }

  async getReconciliations(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/reconciliations?${query}`)
  }

  async getReconciliationDetail(req: IRequest, reconciliationId: string) {
    const query = this.objToQueryString({ reconciliationId })
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/partner/reconciliations/${reconciliationId}?${query}`)
  }

  async updateReconciliationStatus(req: IRequest, reconciliationId: string, data: any) {
    return await callApiHelper.processPutCallApiHelper(req, data, `${this.url}/api/admin/partner/reconciliations/${reconciliationId}/update-status`)
  }
  //#endregion

  //#region REWARD
  async getReward(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reward?${query}`)
  }

  async updateReward(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/reward/update`)
  }

  async createReward(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/reward/create`)
  }

  async updateRewardStatus(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/reward/status`)
  }

  async getLevelPartnerRewardCommission(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reward/get-level-partner-reward-commission?${query}`)
  }
  //#endregion

  //#region REPORT_COMMISSION
  async getReportCommission(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reports/commission?${query}`)
  }

  async getReportCommissionChild(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reports/commission-child?${query}`)
  }

  async getReportCommissionGdv(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reports/commission-gdv?${query}`)
  }

  async getReportCommissionHistory(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reports/commission-history?${query}`)
  }

  async getReportCommissionHistoryMember(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/reports/commission-history-member`)
  }
  //#endregion

  //#region REPORT_CARD_SALE
  async getReportCardSale(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reports/card-sale-report?${query}`)
  }

  async getDetailCardSaleReport(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reports/detail-card-sale-report?${query}`)
  }
  //#endregion

  //#region REPORT_ORDER
  async getReportOrder(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reports/report-order?${query}`)
  }

  async getReportDetailOrder(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reports/report-detail-order?${query}`)
  }

  async getReportDetailOrderPeriod(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/reports/report-detail-order-period?${query}`)
  }
  //#endregion

  //#region VOUCHER
  async getVouchers(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/vouchers?${query}`)
  }

  async importVouchers(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/vouchers/import`)
  }

  async createVoucher(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/vouchers/create`)
  }

  async updateVoucher(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/vouchers/update`)
  }

  async deleteVoucher(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/vouchers/delete`)
  }

  async getVoucherAllocations(req: IRequest, data: any) {
    const query = this.objToQueryString(data)
    return await callApiHelper.processCallGetApiHelper(req, `${this.url}/api/admin/vouchers/allocations?${query}`)
  }
  //#endregion

  //#region ALLOCATE_VOUCHER
  async getAllocateVoucher(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/vouchers/allocate-voucher`)
  }

  async allocateVoucherBatch(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/vouchers/allocate-voucher-batch`)
  }

  async getVoucherHistory(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/vouchers/voucher-history`)
  }

  async getVoucherHistoryDetail(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/vouchers/voucher-history-detail`)
  }

  async allocateVoucher(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/vouchers/allocate-voucher`)
  }

  async revokeVoucher(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/admin/vouchers/revoke-voucher`)
  }
  //#endregion

  //#region PAYMENT TRANSACTION
  ///api/integration/payments/transaction/re-activate
  async reActivateTransaction(req: IRequest, data: any) {
    return await callApiHelper.processCallApiHelper(req, data, `${this.url}/api/integration/payments/transaction/re-activate`)
  }
  //#endregion

  private objToQueryString = (obj: { [x: string]: any }) =>
    Object.keys(obj)
      .map((k) => {
        if (Array.isArray(obj[k])) {
          return `${k}=${JSON.stringify(obj[k])}`
        }
        return `${k}=${obj[k]}`
      })
      .join('&')
}

export const omsApiHelper = new OmsApiHelper()
