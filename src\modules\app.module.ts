import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config/dist/config.module'
import { APP_FILTER } from '@nestjs/core'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { CoreModule } from './core/core.module'
import { hotelModule } from './hotel/hotel.module'
import { purchaseModule } from './purchase/purchase.module'
import { ScheduleModule } from '@nestjs/schedule'
import { HttpModule } from '@nestjs/axios'
import { HttpExceptionFilter } from './common/filters'
import { surveyModule } from './survey/survey.module'
import { wmsModule } from './wms/wms.module'
import { ContactModule } from './contact/contact.module'
import { IntegrationModule } from './integration/integration.module'

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ScheduleModule.forRoot(),
    surveyModule,
    wmsModule,
    IntegrationModule,
    CoreModule,
    hotelModule,
    purchaseModule,
    HttpModule,
    ContactModule,
  ],
  controllers: [AppController],
  providers: [AppService, { provide: APP_FILTER, useClass: HttpExceptionFilter }],
})
export class AppModule { }
