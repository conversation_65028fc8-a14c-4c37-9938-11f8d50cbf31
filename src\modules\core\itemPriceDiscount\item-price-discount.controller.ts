import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common'
import { ItemPriceDiscountService } from './item-price-discount.service'
import { CreateItemPriceDiscountDto, ListCreateItemPriceDiscountDto } from './dto/create-item-price-discount.dto'
import { ListUpdateItemPriceDiscountDto, UpdateItemPriceDiscountDto } from './dto/update-item-price-discount.dto'
import { JwtAuthGuard } from '../../common'

@Controller('item_price_discount')
export class ItemPriceDiscountController {
  constructor(private readonly itemPriceDiscountService: ItemPriceDiscountService) {}

  //list
  @Get('list')
  async list(@Query('itemId') itemId: string) {
    return await this.itemPriceDiscountService.findByItemId(itemId)
  }

  @Post('create')
  @UseGuards(JwtAuthGuard)
  async create(@Body() createItemPriceDiscountDto: ListCreateItemPriceDiscountDto) {
    return await this.itemPriceDiscountService.createDiscountPriceList(createItemPriceDiscountDto)
  }

  @Post('update')
  @UseGuards(JwtAuthGuard)
  async update(@Body() updateItemPriceDiscountDto: ListUpdateItemPriceDiscountDto) {
    return await this.itemPriceDiscountService.updateDiscountPriceList(updateItemPriceDiscountDto)
  }

  @Post('delete')
  @UseGuards(JwtAuthGuard)
  async delete(@Body() data: { id: string }) {
    return await this.itemPriceDiscountService.deleteById(data.id)
  }
}
