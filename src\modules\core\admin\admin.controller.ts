import { Controller, UseGuards, Post, Body, Req, Get, Put, Delete, Param, Query, UploadedFile, UseInterceptors } from '@nestjs/common'
import { JwtAuthGuard } from '../../common/guards'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { omsApiHelper } from '../../../helpers/omsApiHelper'
import { FileInterceptor } from '@nestjs/platform-express'

@ApiBearerAuth()
@ApiTags('Admin')
@UseGuards(JwtAuthGuard)
@Controller('api/admin')
export class AdminController {
  constructor() {}

  //#region STATISTICAL
  @Get('member/statistical/overview')
  public async getStatisticalOverview(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getStatisticalOverview(req, data)
  }

  @Post('member/statistical/details')
  public async getStatisticalDetails(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getStatisticalDetails(req, data)
  }

  @Get('member/list-businesstype-by-member')
  public async getListBusinessTypeByMember(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.listBusinesstypeByMember(req, data)
  }
  //#endregion

  //#region PERIOD_BONUS
  @Post('period-config-bonus/list')
  public async getPeriodBonusList(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getPeriodBonusList(req, data)
  }

  @Post('period-config-bonus/create')
  public async createPeriodBonus(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createPeriodBonus(req, data)
  }

  @Post('period-config-bonus/update')
  public async updatePeriodBonus(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updatePeriodBonus(req, data)
  }

  @Post('period-config-bonus/in-active')
  public async inActivePeriodBonus(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.inActivePeriodBonus(req, data)
  }

  @Post('period-config-bonus/delete')
  public async deletePeriodBonus(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.deletePeriodBonus(req, data)
  }
  //#endregion

  //#region PARTNER_BL
  @Get('partner/list-partner-lv1')
  public async getPartnerLv1List(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getPartnerLv1List(req, data)
  }
  //#endregion

  //#region PARTNER
  @Get('partner/list-partner')
  public async getPartnerList(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getPartnerList(req, data)
  }

  @Get('partner/get-partner-commission')
  public async getPartnerCommission(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getPartnerCommission(req, data)
  }

  @Post('partner-config/import-partner-config')
  public async importPartnerConfig(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.importPartnerConfig(req, data)
  }

  @Get('partner/get-partner-setting-commission')
  public async getPartnerSettingCommission(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getPartnerSettingCommission(req, data)
  }

  @Post('partner/update-partner-setting-commission')
  public async updatePartnerSettingCommission(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updatePartnerSettingCommission(req, data)
  }

  @Post('partner/update-bonus-config')
  public async updateBonusConfig(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateBonusConfig(req, data)
  }

  @Get('partner-config/get-member-partner-config')
  public async getMemberPartnerConfig(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getMemberPartnerConfig(req, data)
  }

  @Put('partner/update-status-partner')
  public async updateStatusPartner(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateStatusPartner(req, data)
  }

  // FILE
  @Post('partner-config/import-partner-config-again')
  @UseInterceptors(FileInterceptor('file'))
  public async importPartnerConfigAgain(@UploadedFile() file: Express.Multer.File, @Req() req: IRequest) {
    return await omsApiHelper.importPartnerConfigAgain(req, file)
  }

  @Get('partner/get-partner-deposit')
  public async getPartnerDeposit(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getPartnerDeposit(req, data)
  }

  @Post('partner/create-partner-deposit')
  public async createPartnerDeposit(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createPartnerDeposit(req, data)
  }

  @Put('partner/update-partner-deposit')
  public async updatePartnerDeposit(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updatePartnerDeposit(req, data)
  }

  @Post('partner/update-info-partner')
  public async updateInfoPartner(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateInfoPartner(req, data)
  }
  //#endregion

  //#region WITHDRAWALS
  @Post('withdraw-statement')
  public async getWithdrawStatement(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getWithdrawStatement(req, data)
  }

  @Get('withdraw-request')
  public async getWithdrawRequest(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getWithdrawRequest(req, data)
  }

  @Put('withdraw-request/reject')
  public async rejectWithdrawRequest(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.rejectWithdrawRequest(req, data)
  }

  @Put('withdraw-request/update-info')
  public async updateWithdrawRequestInfo(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateWithdrawRequestInfo(req, data)
  }

  @Get('withdraw-request/:id')
  public async getWithdrawRequestById(@Param('id') id: string, @Req() req: IRequest) {
    return await omsApiHelper.getWithdrawRequestById(req, id)
  }

  @Post('withdraw-request/re-withdraw')
  public async reWithdraw(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.reWithdraw(req, data)
  }

  @Put('withdraw-request/update-re-withdraw')
  public async updateReWithdraw(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateReWithdraw(req, data)
  }
  //#endregion

  //#region WITHDRAW_PROFILE
  @Post('withdraw-profile')
  public async getWithdrawProfile(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getWithdrawProfile(req, data)
  }

  @Post('withdraw-profile/create')
  public async createWithdrawProfile(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createWithdrawProfile(req, data)
  }

  @Post('withdraw-profile/:id/add-note')
  public async addWithdrawProfileNote(@Param('id') id: string, @Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.addWithdrawProfileNote(req, id, data)
  }

  @Post('withdraw-profile/:id/add-contract-note')
  public async addWithdrawProfileContractNote(@Param('id') id: string, @Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.addWithdrawProfileContractNote(req, id, data)
  }
  //#endregion

  //#region WITHDRAW_STATEMENT
  @Get('withdraw-statement/:id')
  public async getWithdrawStatementById(@Param('id') id: string, @Req() req: IRequest) {
    return await omsApiHelper.getWithdrawStatementById(req, id)
  }

  @Post('withdraw-statement/reject')
  public async rejectWithdrawStatement(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.rejectWithdrawStatement(req, data)
  }

  @Post('withdraw-statement/approve')
  public async approveWithdrawStatement(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.approveWithdrawStatement(req, data)
  }

  @Get('withdraw-statement/get-contract/:id')
  public async getWithdrawStatementContract(@Param('id') id: string, @Req() req: IRequest) {
    return await omsApiHelper.getWithdrawStatementContract(req, id)
  }

  @Post('withdraw-statement/:id/request-approval')
  public async requestWithdrawStatementApproval(@Param('id') id: string, @Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.requestWithdrawStatementApproval(req, id, data)
  }
  //#endregion

  //#region WITHDRAW_TRANSACTION
  @Get('withdraw-transactions')
  public async getWithdrawTransactions(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getWithdrawTransactions(req, data)
  }

  @Post('withdraw-transactions/:id/confirm')
  public async confirmWithdrawTransaction(@Param('id') id: string, @Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.confirmWithdrawTransaction(req, id, data)
  }

  @Put('withdraw-transactions/reject')
  public async rejectWithdrawTransaction(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.rejectWithdrawTransaction(req, data)
  }

  @Post('withdraw-transactions/:id/add-payment-note')
  public async addWithdrawTransactionPaymentNote(@Param('id') id: string, @Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.addWithdrawTransactionPaymentNote(req, id, data)
  }

  @Get('withdraw-transactions/:id')
  public async getWithdrawTransactionById(@Param('id') id: string, @Req() req: IRequest) {
    return await omsApiHelper.getWithdrawTransactionById(req, id)
  }
  //#endregion

  //#region STORE
  @Get('partner/list-store')
  public async getStoreList(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getStoreList(req, data)
  }

  @Post('partner/create-store')
  public async createStore(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createStore(req, data)
  }

  @Put('partner/update-store')
  public async updateStore(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateStore(req, data)
  }

  @Post('partner/import-store')
  public async importStore(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.importStore(req, data)
  }

  @Put('partner/update-status-store')
  public async updateStoreStatus(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateStoreStatus(req, data)
  }

  @Post('partner/search-store')
  public async searchStore(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.searchStore(req, data)
  }
  //#endregion

  //#region TRACKING ORDERS
  @Post('orders/list-order')
  public async getOrderList(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getOrderList(req, data)
  }

  @Post('orders/detail-order')
  public async getOrderDetail(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getOrderDetail(req, data)
  }

  @Post('orders/update-pending-delivery-order')
  public async updatePendingDeliveryOrder(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updatePendingDeliveryOrder(req, data)
  }

  @Post('orders/update-delivering-order')
  public async updateDeliveringOrder(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateDeliveringOrder(req, data)
  }

  @Post('orders/update-delivered-order')
  public async updateDeliveredOrder(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateDeliveredOrder(req, data)
  }

  @Post('orders/cancel-order')
  public async cancelOrder(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.cancelOrder(req, data)
  }

  @Post('orders/print-order')
  public async printOrder(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.printOrder(req, data)
  }
  //#endregion

  //#region CONFIG_CARD_TYPE
  @Get('config-card/type')
  public async getConfigCardType(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getConfigCardType(req, data)
  }

  @Get('config-card/card-type/search')
  public async searchConfigCardType(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.searchConfigCardType(req, data)
  }

  @Post('config-card/create-type')
  public async createConfigCardType(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createConfigCardType(req, data)
  }

  @Put('config-card/update-card-type')
  public async updateConfigCardType(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateConfigCardType(req, data)
  }

  @Put('config-card/delete-card-type')
  public async deleteConfigCardType(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.deleteConfigCardType(req, data)
  }
  //#endregion

  //#region CONFIG_CARD_PERIOD
  @Get('config-card/period')
  public async getConfigCardPeriod(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getConfigCardPeriod(req, data)
  }

  @Get('config-card/period/search')
  public async searchConfigCardPeriod(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.searchConfigCardPeriod(req, data)
  }

  @Post('config-card/create-period')
  public async createConfigCardPeriod(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createConfigCardPeriod(req, data)
  }

  @Put('config-card/update-period')
  public async updateConfigCardPeriod(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateConfigCardPeriod(req, data)
  }

  @Delete('config-card/delete-period')
  public async deleteConfigCardPeriod(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.deleteConfigCardPeriod(req, data)
  }
  //#endregion

  //#region CONFIG_CARD_DURATION
  @Get('config-card/duration')
  public async getConfigCardDuration(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getConfigCardDuration(req, data)
  }

  @Get('config-card/duration/search')
  public async searchConfigCardDuration(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.searchConfigCardDuration(req, data)
  }

  @Post('config-card/create-duration')
  public async createConfigCardDuration(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createConfigCardDuration(req, data)
  }

  @Put('config-card/update-duration')
  public async updateConfigCardDuration(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateConfigCardDuration(req, data)
  }

  @Delete('config-card/delete-duration')
  public async deleteConfigCardDuration(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.deleteConfigCardDuration(req, data)
  }
  //#endregion

  //#region PARTNER_CARD
  @Get('partner/cards')
  public async getPartnerCards(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getPartnerCards(req, data)
  }

  @Post('partner/create-cards')
  public async createPartnerCards(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createPartnerCards(req, data)
  }

  @Put('partner/update-card')
  public async updatePartnerCard(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updatePartnerCard(req, data)
  }

  @Delete('partner/delete-card')
  public async deletePartnerCard(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.deletePartnerCard(req, data)
  }

  @Get('partner/search-card')
  public async searchPartnerCard(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.searchPartnerCard(req, data)
  }

  @Put('partner/update-status-card')
  public async updatePartnerCardStatus(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updatePartnerCardStatus(req, data)
  }

  @Get('member/list-cards-by-member')
  public async listCardsByMember(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.listCardsByMember(req, data)
  }
  //#endregion

  //#region COMBO_CARD
  @Post('partner/assign-combo-card')
  public async assignComboCard(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.assignComboCard(req, data)
  }
  //#endregion

  //#region CARD_SALES_HISTORY
  @Get('payments/card-sales-history')
  public async getCardSalesHistory(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getCardSalesHistory(req, data)
  }

  @Get('payments/card-sales-history-search')
  public async searchCardSalesHistory(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.searchCardSalesHistory(req, data)
  }
  //#endregion

  //#region TRANSACTION
  @Get('payments/transaction-history')
  public async getTransactionHistory(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getTransactionHistory(req, data)
  }

  @Get('payments/detail-transaction-history')
  public async getDetailTransactionHistory(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getDetailTransactionHistory(req, data)
  }
  //#endregion

  //#region MEMBER
  @Get('member/list')
  public async getMember(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getMember(req, data)
  }

  @Get('member/find-detail')
  public async getMemberDetail(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getMemberDetail(req, data)
  }

  @Post('member/update-password/')
  public async updateMemberPassword(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateMemberPassword(req, data)
  }
  //#endregion

  //#region STATEMENT
  @Get('statement/statement-by-product')
  public async getStatementByProduct(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getStatementByProduct(req, data)
  }

  @Get('statement/statement-by-card')
  public async getStatementByCard(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getStatementByCard(req, data)
  }

  @Get('statement/card-statement-by-date')
  public async getCardStatementByDate(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getCardStatementByDate(req, data)
  }

  @Get('statement/product-statement-by-date')
  public async getProductStatementByDate(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getProductStatementByDate(req, data)
  }

  @Get('statement/card-quantity-statement')
  public async getCardQuantityStatement(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getCardQuantityStatement(req, data)
  }

  @Get('statement/deposit-card-statement-by-date')
  public async getDepositCardStatementByDate(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getDepositCardStatementByDate(req, data)
  }
  //#endregion

  //#region RECONCILIATIONS
  @Post('partner/reconciliations/create')
  public async createReconciliation(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createReconciliation(req, data)
  }

  @Get('partner/reconciliations')
  public async getReconciliations(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReconciliations(req, data)
  }

  @Put('partner/reconciliations/:reconciliationId/update-status')
  public async updateReconciliationStatus(@Param('reconciliationId') reconciliationId: string, @Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateReconciliationStatus(req, reconciliationId, data)
  }

  @Get('partner/reconciliations/:reconciliationId')
  public async getReconciliationDetail(@Param('reconciliationId') reconciliationId: string, @Req() req: IRequest) {
    return await omsApiHelper.getReconciliationDetail(req, reconciliationId)
  }
  //#endregion

  //#region REWARD
  @Get('reward')
  public async getReward(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReward(req, data)
  }

  @Post('reward/update')
  public async updateReward(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateReward(req, data)
  }

  @Post('reward/create')
  public async createReward(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createReward(req, data)
  }

  @Post('reward/status')
  public async updateRewardStatus(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateRewardStatus(req, data)
  }

  @Get('partner/get-level-partner-reward-commission')
  public async getLevelPartnerRewardCommission(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getLevelPartnerRewardCommission(req, data)
  }
  //#endregion

  //#region REPORT_COMMISSION
  @Get('reports/commission')
  public async getReportCommission(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReportCommission(req, data)
  }

  @Get('reports/commission-child')
  public async getReportCommissionChild(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReportCommissionChild(req, data)
  }

  @Get('reports/commission-gdv')
  public async getReportCommissionGdv(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReportCommissionGdv(req, data)
  }

  @Get('reports/commission-history')
  public async getReportCommissionHistory(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReportCommissionHistory(req, data)
  }

  @Post('reports/commission-history-member')
  public async getReportCommissionHistoryMember(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReportCommissionHistoryMember(req, data)
  }
  //#endregion

  //#region REPORT_CARD_SALE
  @Get('reports/card-sale-report')
  public async getReportCardSale(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReportCardSale(req, data)
  }

  @Post('config-card/type-by-bussiness')
  public async getCardTypeByBusiness(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getCardTypeByBusiness(req, data)
  }

  @Get('reports/detail-card-sale-report')
  public async getDetailCardSaleReport(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getDetailCardSaleReport(req, data)
  }
  //#endregion

  //#region REPORT_ORDER
  @Get('reports/report-order')
  public async getReportOrder(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReportOrder(req, data)
  }

  @Get('reports/report-detail-order')
  public async getReportDetailOrder(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReportDetailOrder(req, data)
  }

  @Get('reports/report-detail-order-period')
  public async getReportDetailOrderPeriod(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getReportDetailOrderPeriod(req, data)
  }
  //#endregion

  //#region VOUCHER
  @Get('vouchers')
  public async getVouchers(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getVouchers(req, data)
  }

  @Post('vouchers/import')
  public async importVouchers(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.importVouchers(req, data)
  }

  @Post('vouchers/create')
  public async createVoucher(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.createVoucher(req, data)
  }

  @Post('vouchers/update')
  public async updateVoucher(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.updateVoucher(req, data)
  }

  @Post('vouchers/delete')
  public async deleteVoucher(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.deleteVoucher(req, data)
  }
  //#endregion

  //#region ALLOCATE_VOUCHER
  @Get('vouchers/allocations')
  public async getVoucherAllocations(@Query() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getVoucherAllocations(req, data)
  }

  @Post('vouchers/allocate-voucher-batch')
  public async allocateVoucherBatch(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.allocateVoucherBatch(req, data)
  }

  @Post('vouchers/voucher-history')
  public async getVoucherHistory(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getVoucherHistory(req, data)
  }

  @Post('vouchers/voucher-history-detail')
  public async getVoucherHistoryDetail(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.getVoucherHistoryDetail(req, data)
  }

  @Post('vouchers/allocate-voucher')
  public async allocateVoucher(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.allocateVoucher(req, data)
  }

  @Post('vouchers/revoke-voucher')
  public async revokeVoucher(@Body() data: any, @Req() req: IRequest) {
    return await omsApiHelper.revokeVoucher(req, data)
  }
  //#endregion
}
