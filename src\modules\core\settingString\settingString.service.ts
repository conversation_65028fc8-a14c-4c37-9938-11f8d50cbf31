import { Injectable } from '@nestjs/common'
import { SettingStringRepository, UserRepository } from '../../../repositories'

import { In, Like } from 'typeorm'
import { UPDATE_SUCCESS, enumData } from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { SettingStringEntity } from '../../../entities'
import { coreHelper } from '../../../helpers'
import { AppService } from '../../app.service'
import { SettingStringUpdateDto } from './dto/settingStringUpdate.dto'
import { SettingStringUpdateActiveStatusDto } from './dto/settingStringUpdateActiveStatus.dto'

@Injectable()
export class SettingStringService {
  constructor(private readonly repo: SettingStringRepository, private readonly userRepo: UserRepository, private appService: AppService) {}

  public async findAll() {
    return await this.repo.find()
  }

  public async findBannerCustomer() {
    return await this.repo.find({
      where: {
        code: In([
          enumData.SettingString.APP_CUSTOMER_BANNER_1.code,
          enumData.SettingString.APP_CUSTOMER_BANNER_2.code,
          enumData.SettingString.APP_CUSTOMER_BANNER_3.code,
          enumData.SettingString.APP_CUSTOMER_BANNER_4.code,
        ]),
      },
    })
  }

  public async loadData() {
    return await this.repo.find()
  }

  public async find(data: any) {
    return await this.repo.find(data)
  }

  /** Hàm load dữ liệu trong select box */
  public async loadDataSelectBox(user: UserDto) {
    return await this.repo.find({
      select: {
        id: true,
        code: true,
        name: true,
      },
      order: {
        name: 'ASC',
      },
    })
  }

  public async findById(id: string) {
    const foundSettingString = await this.repo.findOne({
      where: {
        id: id,
      },
    })
    if (!foundSettingString) {
      throw new Error('Không tìm thấy setting string theo ID')
    }
    return foundSettingString
  }

  public async findOneByCode(data: FilterOneDto) {
    const foundSettingString = await this.repo.findOne({
      where: {
        code: data.code,
      },
    })
    if (!foundSettingString) {
      throw new Error('Không tìm thấy setting string theo code')
    }

    return foundSettingString
  }

  public async findListByCode(data: any) {
    const foundSettingString = await this.repo.findOne({
      where: {
        code: In(data.lstCode),
      },
    })
    if (!foundSettingString) {
      throw new Error('Không tìm thấy setting string theo danh sách code')
    }

    return foundSettingString
  }

  public async update(user: UserDto, data: SettingStringUpdateDto) {
    await this.repo.delete({})

    const listTask = []

    for (const settingStringItem of data.listSettingString) {
      const newSettingStringItem = new SettingStringEntity()
      newSettingStringItem.name = settingStringItem.name
      newSettingStringItem.code = settingStringItem.code
      newSettingStringItem.value = settingStringItem.value
      newSettingStringItem.valueString = settingStringItem.valueString
      newSettingStringItem.type = settingStringItem.type
      if (settingStringItem.banner && settingStringItem.banner.length > 0) {
        newSettingStringItem.banner = settingStringItem.banner
      } else {
        newSettingStringItem.banner = []
      }
      if (settingStringItem.type == enumData.DataType.switch.code) newSettingStringItem.valueString = settingStringItem.valueString
      if (settingStringItem.type == enumData.DataType.int.code) newSettingStringItem.value = settingStringItem.value
      newSettingStringItem.createdBy = user?.id
      newSettingStringItem.updatedBy = user?.id
      newSettingStringItem.updatedAt = new Date()
      listTask.push(newSettingStringItem)
    }

    await this.repo.insert(listTask)
    return {
      message: UPDATE_SUCCESS,
    }
  }

  public async updateActiveStatus(user: UserDto, data: SettingStringUpdateActiveStatusDto) {
    const { id } = data

    const foundSettingString = await this.repo.findOne({
      where: {
        id,
      },
    })

    if (!foundSettingString) throw new Error('Không tìm thấy setting string')

    foundSettingString.isDeleted = !foundSettingString.isDeleted
    foundSettingString.updatedAt = new Date()

    if (user?.id) foundSettingString.updatedBy = user.id

    const updatedSettingString = await this.repo.save(foundSettingString)
    return {
      message: 'Cập nhật trạng thái thành công!',
      data: updatedSettingString,
    }
  }

  async pagination(user: UserDto, data: PaginationDto) {
    const whereCondition: any = {}
    if (data.where) {
      if (data.where.isDeleted != undefined) whereCondition.isDeleted = data.where.isDeleted
      if (data.where.code) whereCondition.code = Like(`%${data.where.code}%`)
      if (data.where.name) whereCondition.name = Like(`%${data.where.name}%`)
    }

    const defaultSettingStrings = coreHelper.convertObjToArray(enumData.SettingString)
    const resSettingString = await this.repo.findAndCount({
      where: whereCondition,
      order: {
        code: 'ASC',
      },
    })
    const dictSettingString: any = {}
    resSettingString[0].forEach((item) => (dictSettingString[item.code] = item))

    let result = defaultSettingStrings
    const length = defaultSettingStrings.length
    for (let item of result) {
      if (dictSettingString[item.code])
      Object.assign(item, dictSettingString[item.code])
    }
    return [result, length]
  }
}
