import { Module } from '@nestjs/common'
import { ItemPriceDiscountService } from './item-price-discount.service'
import { ItemPriceDiscountController } from './item-price-discount.controller'
import { TypeOrmExModule } from '../../../typeorm'
import { ItemPriceDiscountRepository } from '../../../repositories/core/itemPriceDiscount.repository'

@Module({
  controllers: [ItemPriceDiscountController],
  imports: [TypeOrmExModule.forCustomRepository([ItemPriceDiscountRepository])],
  providers: [ItemPriceDiscountService],
})
export class ItemPriceDiscountModule {}
