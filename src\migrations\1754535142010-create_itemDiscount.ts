import { MigrationInterface, QueryRunner } from 'typeorm'

export class createItemDiscount1754535142010 implements MigrationInterface {
  name = 'createItemDiscount1754535142010'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "item_price_discount" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "createdBy" character varying(36), "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updatedBy" character varying(36), "isDeleted" boolean NOT NULL DEFAULT false, "itemId" uuid NOT NULL, "qualifiedQuantity" integer NOT NULL, "priceOriginal" numeric(20,2) NOT NULL DEFAULT '0', CONSTRAINT "PK_c189c84eb6eb525ac18a956b8d4" PRIMARY KEY ("id"))`,
    )
    await queryRunner.query(`ALTER TABLE "supplier" DROP COLUMN "userId"`)
    await queryRunner.query(`
            ALTER TABLE "permission" DROP CONSTRAINT IF EXISTS "FK_c60570051d297d8269fcdd9bc47"
        `)

    // 2. Đổi kiểu dữ liệu cột (convert kiểu!)
    await queryRunner.query(`
            ALTER TABLE "permission"
            ALTER COLUMN "userId" TYPE uuid USING "userId"::uuid
        `)

    // 3. Đặt lại NOT NULL nếu cần
    await queryRunner.query(`
            ALTER TABLE "permission"
            ALTER COLUMN "userId" SET NOT NULL
        `)

    // 4. Thêm lại FK
    await queryRunner.query(`
            ALTER TABLE "permission"
            ADD CONSTRAINT "FK_c60570051d297d8269fcdd9bc47"
            FOREIGN KEY ("userId") REFERENCES "user"("employeeId") ON DELETE NO ACTION ON UPDATE NO ACTION
        `)
    await queryRunner.query(
      `ALTER TABLE "item_price_discount" ADD CONSTRAINT "FK_660b18aa7402947e7d869fccf9c" FOREIGN KEY ("itemId") REFERENCES "item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "item_price_discount" DROP CONSTRAINT "FK_660b18aa7402947e7d869fccf9c"`)
    await queryRunner.query(`
            ALTER TABLE "permission" DROP CONSTRAINT IF EXISTS "FK_c60570051d297d8269fcdd9bc47"
        `)

    // 2. Đổi lại kiểu dữ liệu (convert về character varying)
    await queryRunner.query(`
            ALTER TABLE "permission"
            ALTER COLUMN "userId" TYPE character varying USING "userId"::text
        `)

    // 3. Đặt lại NOT NULL nếu cần
    await queryRunner.query(`
            ALTER TABLE "permission"
            ALTER COLUMN "userId" SET NOT NULL
        `)

    // 4. Thêm lại FK
    await queryRunner.query(`
            ALTER TABLE "permission"
            ADD CONSTRAINT "FK_c60570051d297d8269fcdd9bc47"
            FOREIGN KEY ("userId") REFERENCES "user"("employeeId") ON DELETE NO ACTION ON UPDATE NO ACTION
        `)
    await queryRunner.query(`ALTER TABLE "supplier" ADD "userId" uuid`)
    await queryRunner.query(`DROP TABLE "item_price_discount"`)
  }
}
