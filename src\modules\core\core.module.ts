import { Module } from '@nestjs/common'
import { AuthModule } from './auth/auth.module'
import { ItemModule } from './item/item.module'
import { PackingModule } from './packing/packing.module'
import { UnitModule } from './unit/unit.module'
import { UploadFileModule } from './uploadFile/uploadFile.module'
import { EmployeeModule } from './employee/employee.module'
import { DepartmentModule } from './department/department.module'
import { ItemComboModule } from './itemCombo/itemCombo.module'
import { ItemTypeModule } from './itemType/itemType.module'
import { CityModule } from './city/city.module'
import { DistrictModule } from './district/district.module'
import { WardModule } from './ward/ward.module'
import { BrandModule } from './brand/brand.module'
import { MediaModule } from './media/media.module'
import { ItemGroupModule } from './itemGroup/itemGroup.module'
import { ItemCategoryModule } from './itemCategory/itemCategory.module'
import { ItemPriceModule } from './productPrice/itemPrice.module'

import { RegionModule } from './region/region.module'

import { SettingStringModule } from './settingString/settingString.module'
import { PartnerModule } from './partner/partner.module'
import { StoreModule } from './store/store.module'
import { SettingMediaModule } from './settingMedia/settingMedia.module'
import { memberModule } from './member/member.module'
import { PublicSettingStringModule } from './publicSettingString/settingString.module'
import { CardPeriodModule } from './cardPeriod/cardPeriod.module'
import { CardDurationModule } from './cardDuration/cardDuration.module'
import { ActionLogModule } from './actionLog/actionLog.module'
import { CardTypeModule } from './cardType/cardType.module'
import { SupplierModule } from './supplier/supplier.module'
import { PartnerMapModule } from './partnerMap/partnerMap.module'
import { NewsCategoryModule } from './newsCategory/newsCategory.module'
import { NewsModule } from './news/news.module'
import { PurchaseOrderModule } from './purchaseOrder/purchaseOrder.module'
import { PublicSettingMediaModule } from './publicSettingMedia/settingMedia.module'
import { WithdrawRequestModule } from './withdrawRequest/withdrawRequest.module'
import { SupplyChainModule } from './supplyChain/supplyChain.module'
import { BankModule } from './bank/bank.module'
import { ContractModule } from './contract/contract.module'
import { DeliveryNoteModule } from './deliveryNote/deliveryNote.module'
import { DeliveryNoteChildModule } from './deliveryNoteChild/deliveryNoteChild.module'
import { PermissionModule } from './permission/permission.module'
import { PoScheduleModule } from './poSchedule/poSchedule.module'
import { TaxModule } from './tax/tax.module'
import { OrderConfigModule } from './order-config/orderConfig.module'
import { AdminModule } from './admin/admin.module'
import { ItemPriceDiscountModule } from './itemPriceDiscount/item-price-discount.module'
@Module({
  imports: [
    ItemPriceDiscountModule,
    ItemModule,
    PackingModule,
    UnitModule,
    UploadFileModule,
    EmployeeModule,
    DepartmentModule,
    AuthModule,
    StoreModule,
    CityModule,
    DistrictModule,
    ItemPriceModule,
    WardModule,
    ItemComboModule,
    ItemTypeModule,
    ItemGroupModule,
    ItemCategoryModule,
    BrandModule,
    MediaModule,
    RegionModule,
    SettingStringModule,
    PartnerModule,
    SettingMediaModule,
    PublicSettingStringModule,
    memberModule,
    PublicSettingMediaModule,
    CardPeriodModule,
    CardDurationModule,
    CardTypeModule,
    ActionLogModule,
    SupplierModule,
    PartnerMapModule,
    NewsCategoryModule,
    NewsModule,
    PurchaseOrderModule,
    WithdrawRequestModule,
    SupplyChainModule,
    BankModule,
    ContractModule,
    DeliveryNoteModule,
    DeliveryNoteChildModule,
    PermissionModule,
    PoScheduleModule,
    OrderConfigModule,
    TaxModule,
    AdminModule,
  ],
})
export class CoreModule {}
