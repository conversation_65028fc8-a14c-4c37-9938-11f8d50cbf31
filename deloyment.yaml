#ConfigMap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ape-bl-api-dev-config
  namespace: ape-bl-dev
data:
  NODE_ENV: 'production'
  TYPEORM_CONNECTION: 'mysql'
  TYPEORM_HOST: 'ape-postgre.c7uzjfmteanl.ap-southeast-1.rds.amazonaws.com'
  TYPEORM_USERNAME: 'apebl-dev'
  TYPEORM_PASSWORD: 'SBlLi#EPt$^!'
  TYPEORM_DATABASE: 'apebl-dev'
  TYPEORM_PORT: '5432'
  TYPEORM_LOGGING: 'false'
  TYPEORM_ENTITIES: 'dist/entities/*.entity.js'
  TYPEORM_SUBSCRIBERS: 'dist/subscribers/*.js'
  TYPEORM_MIGRATIONS: 'dist/migrations/*.js'
  TYPEORM_MIGRATIONS_DIR: 'dist/migrations'
  PORT: '80'
  JWT_SECRET: '<your_secret>'
  JWT_EXPIRY: '10h'

  LINK_UPLOAD_S3: 'ape-bl-dev'
  apiVersion: '2020-03-31'
  KEY_SECRET_MICRO: '1fe864ef-8981-4ee8-bc5e-7544ae79310f'
  AWS_S3_BUCKET_NAME: 'ape-devs-co'
  AWS_S3_ACCESS_KEY_ID: '********************'
  AWS_S3_SECRET_ACCESS_KEY: '/DIKQa//iyYZUvucHau/cRItB+LCkQ76XWspfrcO'
  HOST_OMS_SETTING: 'https://ape-bl-oms-api-dev.apetechs.co'
  MAX_UPLOAD_FILE_SIZE: '5120'

  # SMART LOG
  # KEY_INTEGRATION: 'fe13d51a-fa5a-480a-b764-90557c01993f'
  # KEY-OMS API ADMIN MODULE
  KEY_BL_ADMIN_OMS: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQ'
  # KEY-OMS API INTEGRATION MODULE
  KEY_BL_INTEGRATION_OMS: 'fd526918-a3ff-4205-b3cf-29f0257d29f6'
  # API Authen Sync Tenant Web Foundation
  APE_AUTHEN_API: 'https://authenticator-dev.apetechs.co'

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ape-bl-api-dev
  namespace: ape-bl-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ape-bl-api-dev
  template:
    metadata:
      labels:
        app: ape-bl-api-dev
    spec:
      containers:
        - name: ape-bl-api-dev
          image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-bl-api-dev:latest
          ports:
            - containerPort: 80
          envFrom:
            - configMapRef:
                name: ape-bl-api-dev-config
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
      volumes:
        - name: tz-config
          hostPath:
            path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ape-bl-api-dev
  namespace: ape-bl-dev
  labels:
    run: ape-bl-api-dev
spec:
  type: ClusterIP
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: ape-bl-api-dev
