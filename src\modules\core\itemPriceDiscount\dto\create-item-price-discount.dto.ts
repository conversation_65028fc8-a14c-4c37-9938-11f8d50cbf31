import { ApiProperty } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import { IsInt, IsNotEmpty, IsNumber, IsUUID, Min, ValidateNested } from 'class-validator'

export class CreateItemPriceDiscountDto {
  @ApiProperty({ description: 'Id sản phẩm' })
  @IsNotEmpty()
  @IsUUID()
  itemId: string

  @ApiProperty({ description: 'Số lượng cần đạt để được giảm giá' })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  targetQuantity: number

  @ApiProperty({ description: 'Gi<PERSON> gốc' })
  @IsNotEmpty()
  @Min(0)
  @IsNumber()
  priceOriginal: number
}

export class ListCreateItemPriceDiscountDto {
  @ApiProperty({ description: 'Danh sách Id giá sản phẩm' })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CreateItemPriceDiscountDto)
  items: CreateItemPriceDiscountDto[]
}
