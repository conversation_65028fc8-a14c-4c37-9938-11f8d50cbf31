import { Consumer } from 'sqs-consumer'
import { SQSClient } from '@aws-sdk/client-sqs'
import { Injectable } from '@nestjs/common'
import * as SQS from 'aws-sdk/clients/sqs'
import { ConfigService } from '@nestjs/config'
import { EmailService } from '../../email/email.service'
import { enumData } from '../../../../constants'

declare type SQSMessage = SQS.Types.Message

@Injectable()
export class SQSHandlerService {
  constructor(private readonly configService: ConfigService, private emailService: EmailService) {
    const app = Consumer.create({
      queueUrl: this.configService.get<string>('AWS_SQS_URL') || 'https://sqs.ap-southeast-1.amazonaws.com/077293829360/ape-dev',
      sqs: new SQSClient({
        region: this.configService.get<string>('AWS_SQS_REGION') || 'ap-southeast-1',
        credentials: {
          accessKeyId: this.configService.get<string>('AWS_SQS_ACCESS_KEY_ID') || '********************',
          secretAccessKey: this.configService.get<string>('AWS_SQS_SECRET_ACCESS_KEY') || 'XmMoKqRHZ4cwl0sjFjwW7HfqDeL4NP3fZzAOEe8n',
        },
      }),
      messageAttributeNames: ['All'],
      handleMessage: async (message: SQSMessage) => {
        const msgObject = JSON.parse(message.Body ? message.Body : '')
        switch (msgObject.type) {
          case enumData.SQSMessageType.Test:
            console.log(msgObject.data)
            break
          case enumData.SQSMessageType.Email: {
            try {
              const obj = msgObject.data
              await this.emailService.sendEmail(
                obj.toAddresses,
                obj.subject,
                obj.ccAddresses,
                obj.bccAddresses,
                obj.body_text,
                obj.body_html,
                obj.type,
                obj.isResend ? obj.isResend : false,
                obj.historyId ? obj.historyId : '',
                obj.companyId ? obj.companyId : '',
                obj.userId ? obj.userId : '',
              )
              break
            } catch (error) {
              console.log(error)

              break
            }
          }
          default:
            break
        }
      },
      // handleMessageBatch: this.handleMessageBatch,
    })

    app.on('error', (err: any) => {
      console.error(err.message)
    })

    app.on('processing_error', (err: any) => {
      console.error(err.message)
    })

    app.on('timeout_error', (err) => {
      console.error(err.message)
    })

    app.start()
  }
}
