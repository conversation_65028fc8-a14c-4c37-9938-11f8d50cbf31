import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { WarehouseService } from './warehouse.service'
import { WarehouseUpdateIsActiveDto } from './dto/warehouseUpdateIsActive.dto'
import { WarehouseCreateDto } from './dto/warehouseCreate.dto'
import { WarehouseUpdateDto } from './dto/warehouseUpdate.dto'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { Request as IRequest } from 'express'
import { InboundCreateDto, IncreaseQuantityOrderDto, WarehouseCreateExcelDto, WarehouseProductSafetyCreateDto } from './dto'
import { JwtAuthGuard } from '../../common/guards'
import { ProductExpiryDateDto } from '../../core/item/dto'
import { CurrentUser } from '../../common/decorators'
import { AuthGuard } from '../../common/guards/auth.guard'
@ApiBearerAuth()
@ApiTags('Warehouse')
@UseGuards(AuthGuard)
@Controller('warehouse_public')
export class WarehousePublicController {
  constructor(private readonly service: WarehouseService) { }

  @ApiOperation({ summary: 'Tìm kiếm' })
  @Post('find')
  public async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @ApiOperation({ summary: 'Tìm item trong kho' })
  @Post('find_item')
  public async find_item(@Body() data: any) {
    return await this.service.findItem(data)
  }

  @ApiOperation({ summary: 'Tìm tồn kho của sản phẩm theo hạn sử dụng' })
  @Post('find_warehouse_product_inventory')
  public async findWarehouseProductInventory(@Body() data: ProductExpiryDateDto) {
    return await this.service.findWarehouseProductInventory(data)
  }

  @ApiOperation({ summary: 'Load kho theo store' })
  @Post('load_data')
  public async loadData() {
    return await this.service.loadData()
  }

  @ApiOperation({ summary: 'Phân trang' })
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.pagination(data, req)
  }

  @ApiOperation({ summary: 'Tạo kho' })
  @Post('create_data')
  public async createData(@Body() data: WarehouseCreateDto, @Req() req: IRequest) {
    return await this.service.createData(data, req)
  }

  @ApiOperation({ summary: 'Tạo danh sách Kho với tham số mảng kho' })
  @Post('create_data_with_array')
  public async createDataWithArray(@Body() data: WarehouseCreateDto[], @Req() req: IRequest) {
    return await this.service.createDataWithArray(data, req)
  }

  //delete_data_with_array
  @ApiOperation({ summary: 'Xóa danh sách kho' })
  @Post('delete_data_with_array')
  public async deleteDataWithArray(@Body() data: WarehouseCreateDto[], @Req() req: IRequest) {
    return await this.service.deleteDataWithArray(data, req)
  }

  @ApiOperation({ summary: 'Cập nhật kho' })
  @Post('update_data')
  public async updateData(@Body() data: WarehouseUpdateDto, @Req() req: IRequest) {
    return await this.service.updateData(data, req)
  }

  @ApiOperation({ summary: 'Xóa mềm kho' })
  @Post('update_active')
  public async updateActive(@Body() data: WarehouseUpdateIsActiveDto, @Req() req: IRequest) {
    return await this.service.updateIsDelete(data)
  }

  // cập nhật lock sản phẩm
  @ApiOperation({ summary: 'cập nhật lock sản phẩm' })
  @Post('update_warehouse_lock_quantity')
  public async updateLockOrder(@Body() data: any) {
    return await this.service.updateLockOrder(data)
  }

  // cập nhật hủy lock sản phẩm
  @ApiOperation({ summary: 'cập nhật hủy lock sản phẩm' })
  @Post('update_warehouse_cancel_lock_quantity')
  public async updateCancelLockOrder(@Body() data: any) {
    return await this.service.updateCancelLockOrder(data)
  }

  @ApiOperation({ summary: '' })
  @Post('detail')
  public async findDetail(@Body() data: WarehouseUpdateIsActiveDto, @Req() req: IRequest) {
    return await this.service.findDetail(data, req)
  }

  @ApiOperation({ summary: '' })
  @Post('find_one')
  async findOne(@Body() data: FilterOneDto) {
    return await this.service.findOne(data)
  }

  @ApiOperation({ summary: '' })
  @Post('load_data_warehouse')
  public async loadDataWarehouse(@Body() data: PaginationDto) {
    return await this.service.loadDataWarehouse(data)
  }

  @ApiOperation({ summary: '' })
  @Post('create_data_excel')
  public async createDataExcel(@Body() data: WarehouseCreateExcelDto[], @Req() req: IRequest) {
    return await this.service.createDataExcel(data, req)
  }

  @ApiOperation({ summary: '' })
  @Post('pagination_inventory')
  public async paginationInventory(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.paginationInventory(data, req)
  }

  @ApiOperation({ summary: 'Cập nhật cộng lại số lượng khi xác nhận trả hàng ' })
  @Post('update_quantity_approved_return')
  public async updateQuantityReturn(@Body() data: any, @Req() req: IRequest) {
    return await this.service.updateQuantityReturn(data, req)
  }

  @ApiOperation({ summary: '' })
  @Post('import_quantity_begin')
  public async importQuantityBegin(@Body() data: InboundCreateDto[]) {
    return await this.service.importQuantityBegin(data)
  }

  @ApiOperation({ summary: 'Tạo định mức tồn kho an toàn' })
  @Post('create_warehouse_product_safety')
  public async createWarehouseProductSafety(@Body() data: WarehouseProductSafetyCreateDto, @CurrentUser() user: UserDto) {
    return await this.service.createWarehouseProductSafety(data, user)
  }

  @ApiOperation({ summary: 'Tăng số lượng đã bán của sản phẩm' })
  @Post('increase_quantity_order')
  public async increaseQuantityOrder(@Body() data: [IncreaseQuantityOrderDto]) {
    return await this.service.increaseQuantityOrder(data)
  }

  @ApiOperation({ summary: 'Trừ số lượng đã bán của sản phẩm' })
  @Post('decrease_quantity_order')
  public async decreaseQuantityOrder(@Body() data: [IncreaseQuantityOrderDto]) {
    return await this.service.decreaseQuantityOrder(data)
  }
}
