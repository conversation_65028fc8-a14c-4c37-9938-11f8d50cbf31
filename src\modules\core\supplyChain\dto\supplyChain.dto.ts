import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  IsArray,
  ValidateNested,
  IsEnum,
} from 'class-validator';
import { NSSupplyChain } from '../../../../constants';
import { Type } from 'class-transformer';
import { PageRequest } from '../../../../dto';

export class ListSupplyChaiDto extends PageRequest {
  @ApiPropertyOptional({ description: 'Supply Chain Code' })
  @IsOptional()
  supplyChainCode?: string;

  @ApiPropertyOptional({
    description: `Trạng thái ${Object.values(NSSupplyChain.ESupplierType).join(' | ')}`,
    enum: NSSupplyChain.ESupplierType,
    default: NSSupplyChain.ESupplierType.ACTIVE,
  })
  @IsOptional()
  @IsEnum(NSSupplyChain.ESupplierType)
  status?: NSSupplyChain.ESupplierType;
}

class ConfigTimeDto {
  @ApiProperty({ description: "Số ngày phải duyệt xong" })
  @IsOptional()
  approvalTime?: string;

  @ApiProperty({ description: "Số ngày NCC phải giao tới 3PL " })
  @IsOptional()
  supplierDeliveryTime?: string;

  @ApiProperty({ description: "Số ngày 3PL phải giao hàng tới MBC" })
  @IsOptional()
  thirdPartyDeliveryTime?: string;
}
class CreateSupplyChainDetailDto {
  @ApiProperty({ description: 'ID' })
  @IsOptional()
  id?: string

  @ApiProperty({ description: 'ID vùng' })
  @IsNotEmpty()
  @IsUUID()
  regionId: string;

  @ApiProperty({ description: 'ID nhà phân phối' })
  @IsNotEmpty()
  @IsUUID()
  distributorId: string;

  @ApiProperty({ description: 'ID đơn vị vận chuyển' })
  @IsNotEmpty()
  @IsUUID()
  deliveryId: string;

  @ApiProperty({ description: 'ID nhà cung cấp' })
  @IsNotEmpty()
  @IsUUID()
  packerId: string;

  @ApiProperty({ description: 'ID nhà cung cấp' })
  @IsOptional()
  endUserId?: string;

  @ApiProperty({ description: 'Danh sách các cấp duyệt' })
  @IsNotEmpty()
  @IsArray()
  approvalLst: [ApproverDto]

  // @ApiProperty({ description: 'Danh sách các cấp duyệt' })
  // @IsNotEmpty()
  // configTimeLst: ConfigTimeDto
}

export class SupplyChainApprovalLstDto {
  @ApiProperty({ description: 'Danh sách các cấp duyệt' })
  @IsNotEmpty()
  @IsArray()
  approvalLst: [ApproverDto]
}

export class CreateSupplyChainDto {
  @IsOptional()
  createdBy?: string;

  @ApiProperty({ description: 'ID nhà cung cấp' })
  @IsNotEmpty()
  @IsUUID()
  supplierId: string;

  @ApiProperty({ description: 'Mã chuỗi cung ứng' })
  @IsNotEmpty()
  @IsString()
  supplyChainCode: string;

  @ApiPropertyOptional({ description: 'ID Partner' })
  @IsOptional()
  partnerId?: string;

  @ApiProperty({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  note: string;

  @ApiPropertyOptional({
    description: `Trạng thái ${Object.values(NSSupplyChain.ESupplierType).join(' | ')}`,
    enum: NSSupplyChain.ESupplierType,
    default: NSSupplyChain.ESupplierType.ACTIVE,
  })
  @IsOptional()
  @IsEnum(NSSupplyChain.ESupplierType)
  status?: NSSupplyChain.ESupplierType;

  @ApiPropertyOptional({ description: 'Chi tiết cấu hình phân bổ' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSupplyChainDetailDto)
  details?: CreateSupplyChainDetailDto[];
}

export class UpdateSupplyChainDto {
  @ApiProperty({ description: 'ID Chuỗi cung ứng' })
  @IsOptional()
  @IsUUID()
  supplyChainId: string;

  @ApiProperty({ description: 'ID nhà cung cấp' })
  @IsOptional()
  @IsUUID()
  supplierId: string;

  @ApiProperty({ description: 'Mã chuỗi cung ứng' })
  @IsOptional()
  @IsString()
  supplyChainCode: string;

  @ApiProperty({ description: 'ID Partner' })
  @IsOptional()
  @IsUUID()
  partnerId: string;

  @ApiProperty({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  note: string;

  @ApiPropertyOptional({
    description: `Trạng thái ${Object.values(NSSupplyChain.ESupplierType).join(' | ')}`,
    enum: NSSupplyChain.ESupplierType,
    default: NSSupplyChain.ESupplierType.ACTIVE,
  })
  @IsOptional()
  @IsEnum(NSSupplyChain.ESupplierType)
  status?: NSSupplyChain.ESupplierType;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSupplyChainDetailDto)
  details?: CreateSupplyChainDetailDto[];
}

export class UpdateStatusDto {
  @ApiProperty({ description: 'ID Supply Chain' })
  @IsNotEmpty()
  @IsUUID()
  supplyChainId: string;

  @ApiPropertyOptional({
    description: `Trạng thái ${Object.values(NSSupplyChain.ESupplierType).join(' | ')}`,
    enum: NSSupplyChain.ESupplierType,
    default: NSSupplyChain.ESupplierType.ACTIVE,
  })
  @IsNotEmpty()
  @IsEnum(NSSupplyChain.ESupplierType)
  status?: NSSupplyChain.ESupplierType;
}

class ApproverDto {
  @ApiProperty({ description: 'Id đối tượng duyệt' })
  @IsNotEmpty()
  approverId: string

  @ApiProperty({ description: 'Mã code đối tượng duyệt' })
  @IsNotEmpty()
  approverCode: string

  @ApiProperty({ description: 'Tên đối tượng duyệt' })
  @IsNotEmpty()
  approverName: string

  @ApiProperty({ description: 'Cấp duyệt của đối tượng' })
  @IsNotEmpty()
  approvalLevel: number

  @ApiPropertyOptional({ description: 'Trường hợp cấu hình từng Vùng (tùy chọn)' })
  @IsOptional()
  regionId?: string
}

