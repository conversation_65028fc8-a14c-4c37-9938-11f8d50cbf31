import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../../typeorm'
import {
  CityRepository,
  DeliveryNoteRepository,
  DeliveryNoteTrackingRepository,
  ItemBaseRepository,
  ItemComboRepository,
  ItemPriceRepository,
  ItemRepository,
  PartnerRepository,
  PurchaseOrderHistoryRepository,
  PurchaseOrderItemRepository,
  PurchaseOrderRepository,
  PurchaseOrderSaleOrderRepository,
  PurchaseOrderApproveRepository,
  RegionRepository,
  SupplierRepository,
  SupplyChainConfigApproveRepository,
  SupplyChainConfigDetailRepository,
  SupplyChainConfigRepository,
  UnitRepository,
  UserRepository,
  EmployeeRepository,
  WardRepository,
  DistrictRepository,
  WarehouseRepository,
  WarehouseProductRepository,
  InboundRepository,
  InboundDetailRepository,
  OperationalAreaRepository,
  PurchaseOrderChildRepository,
} from '../../../repositories'
import { PurchaseOrderController } from './purchaseOrder.controller'
import { PurchaseOrderService } from './purchaseOrder.service'
import { InboundModule } from '../../wms/inbound/inbound.module'
import { WarehouseModule } from '../../wms/warehouse/warehouse.module'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      PurchaseOrderRepository,
      PurchaseOrderItemRepository,
      PartnerRepository,
      SupplierRepository,
      ItemRepository,
      ItemBaseRepository,
      RegionRepository,
      CityRepository,
      SupplyChainConfigRepository,
      DeliveryNoteRepository,
      ItemPriceRepository,
      ItemComboRepository,
      SupplyChainConfigDetailRepository,
      SupplyChainConfigApproveRepository,
      DeliveryNoteTrackingRepository,
      UserRepository,
      EmployeeRepository,
      PurchaseOrderHistoryRepository,
      PurchaseOrderSaleOrderRepository,
      PurchaseOrderApproveRepository,
      UnitRepository,
      UserRepository,
      EmployeeRepository,
      CityRepository,
      WardRepository,
      DistrictRepository,
      WarehouseRepository,
      WarehouseProductRepository,
      InboundRepository,
      InboundDetailRepository,
      OperationalAreaRepository,
      PurchaseOrderChildRepository,
    ]),
    InboundModule,
    WarehouseModule,
  ],
  controllers: [PurchaseOrderController],
  providers: [PurchaseOrderService],
  exports: [PurchaseOrderService],
})
export class PurchaseOrderModule {}
