import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEntity } from './base.entity'
import { NSPo } from '../../constants';

@Entity('purchase_order')
export class PurchaseOrderEntity extends BaseEntity {
    @ApiProperty({ description: 'Mã PO' })
    @Column({ unique: true })
    @Index()
    purchaseOrderCode: string;

    @ApiProperty({ description: 'ID Nhà cung cấp / xuất xứ' })
    @Column("uuid")
    supplierId: string

    @ApiProperty({ description: 'ID Nhà phân phối' })
    @Column("uuid", { nullable: true })
    distributorId: string

    @ApiProperty({ description: 'Nhóm các PO khi tạo từ combo nhiều NCC' })
    @Column('varchar')
    purchaseOrderGroup: string

    @ApiProperty({
        description: `Loại PO: ${Object.values(NSPo.EPoType).join(' | ')}`,
        enum: NSPo.EPoType,
        default: NSPo.EPoType.WITHCOMBO,
    })
    @Column({ default: NSPo.EPoType.WITHCOMBO })
    purchaseOrderType: NSPo.EPoType;

    @ApiProperty({ description: 'Người tạo PO' })
    @Column('varchar', { nullable: true })
    creatorId: string;

    @ApiProperty({ description: 'Ghi chú PO' })
    @Column('varchar', { nullable: true })
    note: string;

    @ApiProperty({ description: 'Ngày nhận hàng', example: '2024-11-30T15:40:00.000Z' })
    @Column('timestamptz', { nullable: true })
    deliveryDate: Date; // Ngày nhận hàng mong muốn

    @ApiProperty({ description: 'Ngày duyệt PO mong muốn', example: '2024-11-30T15:40:00.000Z' })
    @Column('timestamptz', { nullable: true })
    approveDate: Date; // Ngày mong muốn duyệt PO

    @ApiProperty({ description: "VAT" })
    @Column("numeric", { default: 0 })
    vat: number;

    @ApiProperty({ description: "Tổng giá trị" })
    @Column('numeric', { precision: 20, scale: 0 })
    totalAmount: number;

    @ApiProperty({ description: "Tống giá trị với VAT" })
    @Column('numeric', { precision: 20, scale: 0 })
    totalAmountVat: number;

    @ApiProperty({ description: "Số lượng SO tham chiếu" })
    @Column("numeric", { default: 0 })
    amountSO: number;

    @ApiProperty({ description: "Danh sách ID SO tham chiếu" })
    @Column("jsonb", { nullable: true })
    soIds: string[];

    @ApiPropertyOptional({
        description: `Trạng thái ${Object.values(NSPo.EPoStatus).join(' | ')}`,
        enum: NSPo.EPoStatus,
        default: NSPo.EPoStatus.NEWLYCREATED,
    })
    @Column({ default: NSPo.EPoStatus.NEWLYCREATED })
    status: NSPo.EPoStatus;

    @ApiPropertyOptional({
        description: `Trạng thái ${Object.values(NSPo.EPoStatus).join(' | ')}`,
        enum: NSPo.EPoStatus,
        default: NSPo.EPoStatus.PENDING_APPROVE,
    })
    @Column({ default: NSPo.EPoStatus.PENDING_APPROVE })
    approveStatus: NSPo.EPoStatus = NSPo.EPoStatus.PENDING_APPROVE;

    @ApiPropertyOptional({
        description: `Trạng thái thanh toán ${Object.values(NSPo.EPoStatusPayment).join(' | ')}`,
        enum: NSPo.EPoStatusPayment,
        default: NSPo.EPoStatusPayment.UNPAID,
    })
    @Column({ default: NSPo.EPoStatusPayment.UNPAID })
    paymentStatus: NSPo.EPoStatusPayment;

    @ApiPropertyOptional({
        description: `Danh sách tệp đính kèm`
    })
    @Column("jsonb", { nullable: true })
    files: string;

    @ApiProperty({ description: "Tiện cọc" })
    @Column('numeric', { precision: 20, scale: 0, nullable: true },)
    deposit: number;

    @ApiProperty({ description: 'Điều kiện giao hàng' })
    @Column('varchar', { nullable: true })
    deliveryTerms: string;

    @ApiPropertyOptional({
        description: `Trạng thái thanh toán ${Object.values(NSPo.EPoTypePayment).join(' | ')}`,
        enum: NSPo.EPoTypePayment,
        default: NSPo.EPoTypePayment.TRANSFER,
    })
    @Column({ default: NSPo.EPoTypePayment.TRANSFER })
    paymentType: NSPo.EPoTypePayment;

    @ApiPropertyOptional({
        description: "Chuỗi cung ứng"
    })
    @Column("uuid", { nullable: true })
    supplyChainId: string

    @ApiPropertyOptional({
        description: "Người duyệt hiện tại"
    })
    @Column("uuid", { nullable: true })
    approverCurrentId: string

    @ApiPropertyOptional({
        description: "Cấp duyệt hiện tại"
    })
    @Column("numeric", { nullable: true })
    approvalLevelCurrent: number

}

