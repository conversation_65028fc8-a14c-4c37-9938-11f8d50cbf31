import { Entity, Column, OneToMany, ManyToOne, <PERSON>inCol<PERSON><PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { DistrictEntity } from './district.entity'
import { SupplierEntity } from './supplier.entity'
import { RegionEntity } from './region.entity'

@Entity('city')
export class CityEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 50, nullable: false })
  code: string

  @Column({ type: 'varchar', length: 250, nullable: false })
  name: string

  @Column({ type: 'varchar', nullable: false, default: "" })
  regionId: string

  @Column({ type: 'varchar', length: 50, nullable: true })
  area: string

  @OneToMany((type) => DistrictEntity, (p) => p.city)
  districts: Promise<DistrictEntity[]>

  @OneToMany((type) => SupplierEntity, (p) => p.city)
  suppliers: Promise<SupplierEntity[]>
}
