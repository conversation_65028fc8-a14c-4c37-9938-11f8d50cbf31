export const enumData = {
  MediaTable: {
    Item: {
      code: 'Item',
      name: 'Item',
    },
    ItemCategory: {
      code: 'ItemCategory',
      name: 'ItemCategory',
    },
    ItemGroup: {
      code: 'ItemGroup',
      name: 'ItemGroup',
    },
    Brand: {
      code: 'Brand',
      name: '<PERSON>',
    },
    ItemType: {
      code: 'ItemCategory',
      name: 'ItemCategory',
    },
  },
  TaxType: {
    BUY: {
      code: 'BUY',
      name: 'Thu<PERSON> mua hàng',
    },
    SELL: {
      code: 'SELL',
      name: '<PERSON>hu<PERSON> bán hàng',
    },
  },
  /** Loại user */
  UserType: {
    Admin: { code: 'Admin', name: 'Admin DMS', description: 'Admin DMS' },
    TenantAdmin: { code: 'TenantAdmin', name: 'Admin của đối tác' },
    TenantEmployee: { code: 'TenantEmployee', name: '<PERSON><PERSON><PERSON> viên của đối tác' },
    CompanyAdmin: { code: 'CompanyAdmin', name: '<PERSON><PERSON> của công ty' },
    CompanyEmployee: { code: 'CompanyEmployee', name: '<PERSON><PERSON><PERSON> viên của công ty' },
    Customer: { code: 'Customer', name: 'Khách hàng' },
    Employee: { code: 'Customer', name: 'Khách hàng' },

    AdminEmp: { code: 'AdminEmp', name: 'Nhân viên admin' },
    Partner: { code: 'Partner', name: 'Bưu cục' },
    PartnerEmp: { code: 'PartnerEmp', name: 'Nhân viên bưu cục' },
    PartnerSale: { code: 'PartnerSale', name: 'Sale bưu cục' },
    PartnerSaleEmp: { code: 'PartnerSaleEmp', name: 'Nhân viên sale bưu cục' },

    Supplier: { code: 'Supplier', name: 'Nhà cung cấp' },
  },
  SQSMessageType: {
    Test: 'test',
    Email: 'email',
  },
  EmailStatus: {
    Success: {
      code: 'Success',
      name: 'Gửi email thành công',
    },
    Fail: {
      code: 'Fail',
      name: 'Gửi email thất bại',
    },
  },

  DataType: {
    string: { code: 'string', name: 'Kiểu chuỗi', format: '' },
    Number: { code: 'Number', name: 'Số' },
    int: { code: 'int', name: 'Kiểu sổ nguyên', format: '' },
    float: { code: 'float', name: 'Kiểu sổ thập phân', format: '' },
    date: { code: 'date', name: 'Kiểu ngày', format: 'dd/MM/yyyy' },
    dateTime: { code: 'dateTime', name: 'Kiểu ngày giờ', format: 'dd/MM/yyyy HH:mm:ss' },
    time: { code: 'time', name: 'Kiểu giờ', format: 'HH:mm:ss' },
    boolean: { code: 'boolean', name: 'Kiểu checkbox', format: '' },
    switch: { code: 'switch', name: 'Kiểu switch', format: false },
    File: { code: 'File', name: 'File' },
  },
  SupplierStatus: {
    MoiDangKy: { code: 'MoiDangKy', name: 'Mới đăng ký', color: 'darkblue' },
    DaDuyet: { code: 'DaDuyet', name: 'Đã duyệt', color: 'darkgreen' },
    Huy: { code: 'Huy', name: 'Huỷ', color: 'darkred' },
  },

  SettingString: {
    REFERENCE_CODE_BALA: {
      code: 'BALACOM_BENEFIT',
      name: 'BALA COMMUNITY BENEFIT',
      valueString: '0908161075',
      type: 'string',
      isDeleted: false,
    },
    EditTimeOffsetOrderScan: {
      code: 'EDIT_TIME_OFFSET_ORDER_SCAN',
      name: 'Biên độ thời gian cho phép chỉnh sửa đơn',
      value: 0,
      isDeleted: false,
      type: 'int',
    },
    FrequencyDaysOrderScan: { code: 'FREQUENCY_DAYS_ORDER_SCAN', name: 'Tần xuất số ngày / 1 lần quét đơn', value: 0, isDeleted: false, type: 'int' },
    StartDateOrderScan: { code: 'START_DATE_ORDER_SCAN', name: 'Ngày bắt đầu quét đơn hàng tháng', value: 0, isDeleted: false, type: 'int' },
    ScanTimeOffsetOrderScan: {
      code: 'SCAN_TIME_OFFSET_ORDER_SCAN',
      name: 'Biên độ thời gian quét đơn so với thời gian giao',
      value: 0,
      isDeleted: false,
      type: 'int',
    },
    LIMIT_WITHDRAW_COMMISSION: {
      code: 'LIMIT_WITHDRAW_COMMISSION',
      name: 'Giới hạn hoa hồng rút tiền',
      value: 0,
      isDeleted: false,
      type: 'int',
    },
    WITH_DRAW_LIMIT: {
      code: 'WITH_DRAW_LIMIT',
      name: 'Giới hạn rút tiền',
      valueString: 0,
      isDeleted: false,
      type: 'int',
    },
    IS_REQUIRED_REFERRAL_CODE: {
      code: 'IS_REQUIRED_REFERRAL_CODE',
      name: 'Cộng tác viên tự do',
      valueString: null,
      isDeleted: false,
      type: 'switch',
    },
    IS_REQUIRED_CONTRACT_FOR_COMPANY: {
      code: 'IS_REQUIRED_CONTRACT_FOR_COMPANY',
      name: 'Hợp đồng cho doanh nghiệp',
      valueString: null,
      isDeleted: false,
      type: 'switch',
    },
    IS_REQUIRED_CONTRACT_FOR_PERSON: {
      code: 'IS_REQUIRED_CONTRACT_FOR_PERSON',
      name: 'Hợp đồng cho cá nhân',
      valueString: null,
      isDeleted: false,
      type: 'switch',
    },
    DELIVERY_SCHEDULE_DAYS: {
      code: 'DELIVERY_SCHEDULE_DAYS',
      name: 'Số khoảng ngày giao được thay đổi ở từng đơn hàng',
      value: 0,
      isDeleted: false,
      type: 'int',
    },

    LIMIT_PAYMENT_ORDER: {
      code: 'LIMIT_PAYMENT_ORDER',
      name: 'Giá trị đơn hàng tối thiểu',
      value: 0,
      isDeleted: false,
      type: 'int',
    },
    APP_AN_SINH_1: {
      code: 'APP_AN_SINH_1',
      name: 'Banner an sinh bình ổn',
      valueString: null,
      isDeleted: false,
    },
    APP_AN_SINH_2: {
      code: 'APP_AN_SINH_2',
      name: 'Banner an sinh bình ổn',
      valueString: null,
      isDeleted: false,
    },
    APP_AN_SINH_3: {
      code: 'APP_AN_SINH_3',
      name: 'Banner an sinh bình ổn',
      valueString: null,
      isDeleted: false,
    },
    APP_AN_SINH_4: {
      code: 'APP_AN_SINH_4',
      name: 'Banner an sinh bình ổn',
      valueString: null,
      isDeleted: false,
    },
    APP_HEALTH_1: {
      code: 'APP_HEALTH_1',
      name: 'Banner sức khỏe',
      valueString: null,
      isDeleted: false,
    },
    APP_HEALTH_2: {
      code: 'APP_HEALTH_2',
      name: 'Banner sức khỏe',
      valueString: null,
      isDeleted: false,
    },
    APP_HEALTH_3: {
      code: 'APP_HEALTH_3',
      name: 'Banner sức khỏe',
      valueString: null,
      isDeleted: false,
    },
    APP_HEALTH_4: {
      code: 'APP_HEALTH_4',
      name: 'Banner sức khỏe',
      valueString: null,
      isDeleted: false,
    },
    APP_LED_AD_1: {
      code: 'APP_LED_AD_1',
      name: 'Banner quảng cáo đèn led',
      valueString: null,
      isDeleted: false,
    },
    APP_LED_AD_2: {
      code: 'APP_LED_AD_2',
      name: 'Banner quảng cáo đèn led',
      valueString: null,
      isDeleted: false,
    },
    APP_LED_AD_3: {
      code: 'APP_LED_AD_3',
      name: 'Banner quảng cáo đèn led',
      valueString: null,
      isDeleted: false,
    },
    APP_LED_AD_4: {
      code: 'APP_LED_AD_4',
      name: 'Banner quảng cáo đèn led',
      valueString: null,
      isDeleted: false,
    },
    APP_SME360_1: {
      code: 'APP_SME360_1',
      name: 'Banner SME360',
      valueString: null,
      isDeleted: false,
    },
    APP_SME360_2: {
      code: 'APP_SME360_2',
      name: 'Banner SME360',
      valueString: null,
      isDeleted: false,
    },
    APP_SME360_3: {
      code: 'APP_SME360_3',
      name: 'Banner SME360',
      valueString: null,
      isDeleted: false,
    },
    APP_SME360_4: {
      code: 'APP_SME360_4',
      name: 'Banner SME360',
      valueString: null,
      isDeleted: false,
    },
    APP_TOURISM_1: {
      code: 'APP_TOURISM_1',
      name: 'Banner du lịch',
      valueString: null,
      isDeleted: false,
    },
    APP_TOURISM_2: {
      code: 'APP_TOURISM_2',
      name: 'Banner du lịch',
      valueString: null,
      isDeleted: false,
    },
    APP_TOURISM_3: {
      code: 'APP_TOURISM_3',
      name: 'Banner du lịch',
      valueString: null,
      isDeleted: false,
    },
    APP_TOURISM_4: {
      code: 'APP_TOURISM_4',
      name: 'Banner du lịch',
      valueString: null,
      isDeleted: false,
    },
    APP_CUSTOMER_BANNER_1: {
      code: 'WEB_CUSTOMER_BANNER_1',
      name: 'Banner app customer 1',
      valueString: null,
      isDeleted: false,
      // type: 'ckEditor',
    },
    APP_CUSTOMER_BANNER_2: {
      code: 'WEB_CUSTOMER_BANNER_2',
      name: 'Banner app customer 2',
      valueString: null,
      isDeleted: false,
      // type: 'ckEditor',
    },
    APP_CUSTOMER_BANNER_3: {
      code: 'WEB_CUSTOMER_BANNER_3',
      name: 'Banner app customer 3',
      valueString: null,
      isDeleted: false,
      // type: 'ckEditor',
    },
    APP_CUSTOMER_BANNER_4: {
      code: 'WEB_CUSTOMER_BANNER_4',
      name: 'Banner app customer 4',
      valueString: null,
      isDeleted: false,
      // type: 'ckEditor',
    },
    APP_FOOD_1: {
      code: 'APP_FOOD_1',
      name: 'Banner thực phẩm 1',
      valueString: null,
      isDeleted: false,
      type: 'string',
    },
    APP_FOOD_2: {
      code: 'APP_FOOD_2',
      name: 'Banner thực phẩm 2',
      valueString: null,
      isDeleted: false,
      type: 'string',
    },
    APP_FOOD_3: {
      code: 'APP_FOOD_3',
      name: 'Banner thực phẩm 3',
      valueString: null,
      isDeleted: false,
      type: 'string',
    },
    APP_FOOD_4: {
      code: 'APP_FOOD_4',
      name: 'Banner thực phẩm 4',
      valueString: null,
      isDeleted: false,
      type: 'string',
    },
    APP_SALE_BANNER_1: {
      code: 'APP_SALE_BANNER_1',
      name: 'Banner app sale',
      valueString: null,
      isDeleted: false,
      // type: 'ckEditor',
    },
    APP_SALE_BANNER_2: {
      code: 'APP_SALE_BANNER_2',
      name: 'Banner app sale',
      valueString: null,
      isDeleted: false,
      // type: 'ckEditor',
    },
    APP_SALE_BANNER_3: {
      code: 'APP_SALE_BANNER_3',
      name: 'Banner app sale',
      valueString: null,
      isDeleted: false,
      // type: 'ckEditor',
    },
    APP_SALE_BANNER_4: {
      code: 'APP_SALE_BANNER_4',
      name: 'Banner app sale',
      valueString: null,
      isDeleted: false,
      // type: 'ckEditor',
    },

    APP_HOME_BANNER_1: {
      code: 'APP_HOME_BANNER_1',
      name: 'Banner trang chủ',
      valueString: null,
      isDeleted: false,
      type: 'string',
    },
    APP_HOME_BANNER_2: {
      code: 'APP_HOME_BANNER_2',
      name: 'Banner trang chủ',
      valueString: null,
      isDeleted: false,
      type: 'string',
    },
    APP_HOME_BANNER_3: {
      code: 'APP_HOME_BANNER_3',
      name: 'Banner trang chủ',
      valueString: null,
      isDeleted: false,
      type: 'string',
    },
    APP_HOME_BANNER_4: {
      code: 'APP_HOME_BANNER_4',
      name: 'Banner trang chủ',
      valueString: null,
      isDeleted: false,
      type: 'string',
    },

    APP_SALE_TERM: {
      code: 'APP_SALE_TERM',
      name: 'Điều khoản App Sale',
      valueString: null,
      isDeleted: false,
      type: 'ckEditor',
    },

    APP_PARTNER_TERM: {
      code: 'APP_PARTNER_TERM',
      name: 'Điều khoản đăng ký App Balacom CTV',
      valueString: null,
      isDeleted: false,
      type: 'ckEditor',
    },
  },
  SettingMedia: {
    LED_ADVERTISEMENT: {
      type: 'LED_ADVERTISEMENT',
      typeName: 'Quảng cáo Led',
      valueString: null,
      isDeleted: false,
    },
    SME360: {
      type: 'SME360',
      typeName: 'SME360',
      valueString: null,
      isDeleted: false,
    },
    HEALTH: {
      type: 'HEALTH',
      typeName: 'Sức khỏe',
      valueString: null,
      isDeleted: true,
    },
    TOURISM: {
      type: 'TOURISM',
      typeName: 'Du lịch',
      valueString: null,
      isDeleted: false,
    },
    SOCIAL_SECURITY_CARD: {
      type: 'SOCIAL_SECURITY_CARD',
      typeName: 'Thẻ An Sinh Bình Ổn ',
      valueString: null,
      isDeleted: false,
    },
    FOOD: {
      type: 'FOOD',
      typeName: 'Thực phẩm cân bằng',
      valueString: null,
      isDeleted: false,
    },
    RETAIL: {
      type: 'RETAIL',
      typeName: 'Bán lẻ',
      valueString: null,
      isDeleted: false,
    },
    EDUCATION: {
      type: 'EDUCATION',
      typeName: 'Giáo dục',
      valueString: null,
      isDeleted: false,
    },
  },

  StatusFilter: {
    All: { value: 0, code: 'all', name: 'Tất cả' },
    Active: { value: 1, code: 'active', name: 'Đang hoạt động' },
    InActive: { value: 2, code: 'inactive', name: 'Ngưng hoạt động' },
  },

  /** Kiểu dữ liệu của câu hỏi */
  QuestionType: {
    String: { code: 'String', name: 'Free Text' },
    Number: { code: 'Number', name: 'Số' },
    File: { code: 'File', name: 'File' },
    List: { code: 'List', name: 'Danh sách' },
    Date: { code: 'Date', name: 'Ngày giờ' },
    Checkbox: { code: 'Checkbox', name: 'Checkbox' },
  },

  /** trạng thái làm khảo sát */
  SurveyQuestionStatus: {
    Complete: { code: 'Complete', name: 'Đã làm khảo sát' },
    New: { code: 'New', name: 'Chưa làm khảo sát' },
  },

  /**trạng thái notify */
  NotifyStatus: {
    Read: { name: 'Đã đọc', code: 'Read' },
    New: { name: 'Chưa đọc', code: 'New' },
  },

  /** Trạng thái làm phiếu khảo sát của nhân viên */
  SurveyMemberStatus: {
    NotStart: { code: 'NOT_START', name: 'Chưa làm' },
    InProgress: { code: 'IN_PROGRESS', name: 'Đang làm' },
    Done: { code: 'DONE', name: 'Đã làm' },
  },

  /** trạng thái phiếu khảo sát */
  SurveyStatus: {
    New: { code: 'New', name: 'Mới tạo', color: '#096dd9' },
    Doing: { code: 'Doing', name: 'Đang thực hiện', color: '#EAB042' },
    Complete: { code: 'Complete', name: 'Hoàn thành khảo sát', color: '#008000' },
    Cancel: { code: 'Cancel', name: 'Hủy khảo sát', color: '#cf1322' },
    Pending: { code: 'Pending', name: 'Ngưng hoạt động', color: '#f50' },
  },

  CategoryStatus: {
    New: { code: 'New', name: 'Mới tạo', color: '#096dd9' },
    Update: { code: 'Update', name: 'Cập nhật', color: '#EAB042' },
    Cancel: { code: 'Cancel', name: 'Hủy', color: '#cf1322' },
    Pending: { code: 'Pending', name: 'Ngưng hoạt động', color: '#f50' },
  },

  TopicStatus: {
    New: { code: 'New', name: 'Mới tạo', color: '#096dd9' },
    Update: { code: 'Update', name: 'Cập nhật', color: '#EAB042' },
    Cancel: { code: 'Cancel', name: 'Hủy', color: '#cf1322' },
    Pending: { code: 'Pending', name: 'Ngưng hoạt động', color: '#f50' },
  },

  /** Loại phiếu xuất kho */
  OutboundType: {
    ORDER: { code: 'ORDER', name: 'Xuất kho từ order' },
    INTERNAL_WAREHOUSE: { code: 'INTERNAL_WAREHOUSE', name: 'Xuất kho nội bộ' },
    WAREHOUSE_TRANSFER: { code: 'WAREHOUSE_TRANSFER', name: 'Xuất kho chuyển kho' },
    CHECK_INVENTORY: { code: 'CHECK_INVENTORY', name: 'Xuất kho kiểm kho' },
    DAMAGED_GOODS: { code: 'DAMAGED_GOODS', name: 'Xử lý hàng hư hỏng' },
    EXPORT_ORDER: { code: 'EXPORT_ORDER', name: 'Xuất đơn' },
    COMBO: { code: 'COMBO', name: 'Xuất kho tạo combo' },
  },

  // #region Kho
  /** Loại phiếu nhập kho */
  InboundType: {
    PO: { code: 'PO', name: 'Nhập kho PO' },
    WAREHOUSE_TRANSFER: { code: 'WAREHOUSE_TRANSFER', name: 'Nhập kho chuyển kho' },
    COMBO: { code: 'COMBO', name: 'Nhập kho combo' },
    CHECK_INVENTORY: { code: 'CHECK_INVENTORY', name: 'Nhập kho kiểm kho' },
    ENTER_INTERNAL_WAREHOUSE: { code: 'ENTER_INTERNAL_WAREHOUSE', name: 'Nhập kho nội bộ' },
  },

  /** Trạng thái phiếu chuyển kho */
  WarehouseTransferStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#2453F8' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: '#E11F1F' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25' },
    SENT_SHIPPER: { code: 'SENT_SHIPPER', name: 'Đã gửi shipper', color: '#EED554' },
    SHIPPER_CONFIRMED: { code: 'SHIPPER_CONFIRMED', name: 'Shipper xác nhận', color: '#FF981F' },
    WAITING: { code: 'WAITING', name: 'Chờ chuyển kho', color: '#904837' },
    DELIVERING: { code: 'DELIVERING', name: 'Đang chuyển', color: '#DA4CB2' },
    FINISH: { code: 'FINISH', name: 'Hoàn thành', color: '#68BA00' },
    CONFIRMED_TRANSFER: { code: 'CONFIRMED_TRANSFER', name: 'Xác nhận đã chuyển', color: '#00A4A4' },
  },

  /** Trạng thái PNK */
  InboundStatus: {
    New: { code: 'New', name: 'Mới tạo', color: '#d1e8ff', colorText: '#1890ff' },
    Cancel: { code: 'Cancel', name: 'Hủy', color: '#fed8de', colorText: '#9b0819' },
    Approved: { code: 'Approved', name: 'Đã duyệt', color: '#d5f2df', colorText: '#005c20' },
  },

  /** Hình thức vận chuyển */
  TransportType: {
    SEA: { code: 'SEA', name: 'Đường biển' },
    AIR: { code: 'AIR', name: 'Đường bay' },
    ROAD: { code: 'ROAD', name: 'Đường bộ' },
  },

  /** Trạng thái phiếu kiểm kho */
  CheckInventoryStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#2453F8' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: '#E11F1F' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25' },
  },

  /** Trạng thái phiếu xuất kho  */
  OutboundStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#2453F8' },
    RECEIVING_WAREHOUSE: { code: 'RECEIVING_WAREHOUSE', name: 'Kho tiếp nhận', color: '#d5f2df' },
    PREPARING: { code: 'PREPARING', name: 'Đơn đang soạn', color: '#d5f2df' },
    PREPARED: { code: 'PREPARED', name: 'Đơn đã soạn', color: '#1f1c43' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: '#E11F1F' },
  },
  CategoryType: {
    Customer: { code: 'Customer', name: 'Khách hàng' },
    Farmer: { code: 'Farmer', name: 'Nông hộ' },
    Company: { code: 'Company', name: 'Doanh nghiệp' },
  },
  MediaType: {
    LedAdvertising: { code: 'LEDADVERTISING', name: 'Quảng cáo LED' },
    SocialCard: { code: 'SOCIALCARD', name: 'Thẻ An Sinh Bình Ổn' },
    Tourism: { code: 'TOURISM', name: 'Du lịch' },
    Health: { code: 'HEALTH', name: 'Sức khỏe' },
    Sme360: { code: 'SME360', name: 'SME 360' },
    Food: { code: 'FOOD', name: 'Thực phẩm nhân đạo' },
  },
  Type: {
    ThemMoi: { code: 'ThemMoi', name: 'Thêm mới', description: 'thêm mới' },
    ChinhSua: { code: 'ChinhSua', name: 'Chỉnh sửa', description: 'chỉnh sửa' },
    CapNhat: { code: 'CapNhat', name: 'Cập nhật', description: 'cập nhật' },
    Xoa: { code: 'ChinhSXoaua', name: 'Xóa', description: 'Xóa' },
  },
  ActionLog: {
    Setting: {
      code: 'Setting',
      name: 'Thiết lập',
      View: { name: 'Xem', code: 'Setting_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Update', value: false },
    },
    Setting_Employee: {
      code: 'Setting_Employee',
      name: 'Danh sách nhân viên',
      View: { name: 'Xem', code: 'Setting_Employee_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Employee_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Employee_Update', value: false },
    },
    Setting_Department: {
      code: 'Setting_Department',
      name: 'Phòng ban',
      View: { name: 'Xem', code: 'Setting_Department_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Department_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Department_Update', value: false },
    },
    Setting_Permission: {
      code: 'Setting_Permission',
      name: 'Phân quyền',
      View: { name: 'Xem', code: 'Setting_Permission_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Permission_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Permission_Update', value: false },
    },
    Setting_Region: {
      code: 'Setting_Region',
      name: 'Thiết lập vùng',
      View: { name: 'Xem', code: 'Setting_Region_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Region_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Region_Update', value: false },
    },
    Setting_City: {
      code: 'Setting_City',
      name: 'Danh sách Tỉnh/Thành phố',
      View: { name: 'Xem', code: 'Setting_City_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_City_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_City_Update', value: false },
    },
    Setting_District: {
      code: 'Setting_District',
      name: 'Danh sách Quận/Huyện',
      View: { name: 'Xem', code: 'Setting_District_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_District_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_District_Update', value: false },
    },
    Setting_Ward: {
      code: 'Setting_Ward',
      name: 'Danh sách Phường/Xã',
      View: { name: 'Xem', code: 'Setting_Ward_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Ward_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Ward_Update', value: false },
    },
    Setting_Customer: {
      code: 'Setting_Customer',
      name: 'Danh sách khách hàng',
      View: { name: 'Xem', code: 'Setting_Customer_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Customer_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Customer_Update', value: false },
    },
    Setting_Supplier: {
      code: 'Setting_Supplier',
      name: 'Thiết lập nhà cung cấp',
      View: { name: 'Xem', code: 'Setting_Supplier_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Supplier_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Supplier_Update', value: false },
    },
    Setting_Product: {
      code: 'Setting_Product',
      name: 'Danh sách sản phẩm',
      View: { name: 'Xem', code: 'Setting_Product_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Product_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Product_Update', value: false },
    },
    Setting_Product_Price: {
      code: 'Setting_Product',
      name: 'Danh sách giá sản phẩm',
      View: { name: 'Xem', code: 'Setting_Product_Price_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Product_Price__Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Product_Price_Update', value: false },
    },
    Setting_Warehouse: {
      code: 'Setting_Warehouse',
      name: 'Danh sách kho',
      View: { name: 'Xem', code: 'Setting_Warehouse_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Warehouse_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Warehouse_Update', value: false },
    },
    Setting_Reason: {
      code: 'Setting_Reason',
      name: 'Danh sách lý do',
      View: { name: 'Xem', code: 'Setting_Reason_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Reason_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Reason_Update', value: false },
    },
    Setting_Packing: {
      code: 'Setting_Packing',
      name: 'Quy Cách Đóng Gói',
      View: { name: 'Xem', code: 'Setting_Packing_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Packing_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Packing_Update', value: false },
    },
    Setting_PaymentCategory: {
      code: 'Setting_PaymentCategory',
      name: 'Danh mục phiếu chi',
      View: { name: 'Xem', code: 'Setting_PaymentCategory_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_PaymentCategory_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_PaymentCategory_Update', value: false },
    },
    Receipt_Category: {
      code: 'Receipt_Category',
      name: 'Danh mục phiếu thu',
      View: { name: 'Xem', code: 'Receipt_Category_View', value: false },
      Add: { name: 'Thêm', code: 'Receipt_Category_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Receipt_Category_Update', value: false },
    },
    Setting_Partner: {
      code: 'Setting_Partner',
      name: 'Thiết lập chành xe',
      View: { name: 'Xem', code: 'Setting_Partner_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Partner_View', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Partner_View', value: false },
    },
    CostAllocationType: {
      code: 'CostAllocationType',
      name: 'Thiết lập chi phí',
      View: { name: 'Xem', code: 'CostAllocationType_View', value: false },
      Add: { name: 'Thêm', code: 'CostAllocationType_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'CostAllocationType_Update', value: false },
    },
    CostAllocation: {
      code: 'CostAllocation',
      name: 'Phân bổ chi phí',
      View: { name: 'Xem', code: 'CostAllocation_View', value: false },
      Add: { name: 'Thêm', code: 'CostAllocation_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'CostAllocation_Update', value: false },
    },
    Inbound: {
      code: 'Inbound',
      name: 'Nhập kho',
      View: { name: 'Xem', code: 'Inbound_View', value: false },
      Add: { name: 'Thêm', code: 'Inbound_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Inbound_Update', value: false },
    },
    Outbound: {
      code: 'Outbound',
      name: 'Xuất kho',
      View: { name: 'Xem', code: 'Outbound_View', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Outbound_Update', value: false },
    },
    CheckInventory: {
      code: 'CheckInventory',
      name: 'Kiểm kho',
      View: { name: 'Xem', code: 'CheckInventory_View', value: false },
      Update: { name: 'Chỉnh sửa', code: 'CheckInventory_Update', value: false },
    },
    OrderReturn: {
      code: 'OrderReturn',
      name: 'Trả Hàng',
      View: { name: 'Xem', code: 'OrderReturn_View', value: false },
      Add: { name: 'Thêm', code: 'OrderReturn_Add', value: false },
    },
    Orders: {
      code: 'Orders',
      name: 'Kiểm tra đơn hàng',
      View: { name: 'Xem', code: 'Orders_View', value: false },
      Add: { name: 'Thêm', code: 'Orders_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Orders_Update', value: false },
    },
    OrderGroup: {
      code: 'OrderGroup',
      name: 'Quản lí gộp đơn',
      View: { name: 'Xem', code: 'OrderGroup_View', value: false },
      Add: { name: 'Thêm', code: 'OrderGroup_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'OrderGroup_Update', value: false },
    },
    OrderDoccument: {
      code: 'OrderDoccument',
      name: 'Đơn Chứng Từ',
      View: { name: 'Xem', code: 'OrderDoccument_View', value: false },
      Add: { name: 'Thêm', code: 'OrderDoccument_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'OrderDoccument_Update', value: false },
    },
    InboundReturn: {
      code: 'InboundReturn',
      name: 'Hoàn gốc',
      View: { name: 'Xem', code: 'InboundReturn_View', value: false },
      Update: { name: 'Chỉnh sửa', code: 'InboundReturn_Update', value: false },
    },
    Collection: {
      code: 'Collection',
      name: 'Thu',
      View: { name: 'Xem', code: 'Collection_View', value: false },
      Add: { name: 'Thêm', code: 'Collection_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Collection_Update', value: false },
    },
    Payment: {
      code: 'Payment',
      name: 'Phiếu Chi',
      View: { name: 'Xem', code: 'Payment_View', value: false },
      Add: { name: 'Thêm', code: 'Payment_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Payment_Update', value: false },
    },
    Receipt: {
      code: 'Receipt',
      name: 'Phiếu Thu',
      View: { name: 'Xem', code: 'Receipt_View', value: false },
      Add: { name: 'Thêm', code: 'Receipt_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Receipt_Update', value: false },
    },
    PO: {
      code: 'PO',
      name: 'PO',
      View: { name: 'Xem', code: 'PO_View', value: false },
      Add: { name: 'Thêm', code: 'PO_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'PO_Update', value: false },
    },
    ChangPassWord: {
      code: 'ChangPassWord',
      name: 'Cập nhật mật khẩu',
      View: { name: 'Xem', code: 'ChangPassWord_View', value: false },
      Add: { name: 'Thêm', code: 'ChangPassWord_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'ChangPassWord_Update', value: false },
    },
    Setting_Bank: {
      code: 'Setting_Bank',
      name: 'Danh sách ngân hàng',
      View: { name: 'Xem', code: 'Setting_Bank_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Bank_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Bank_Update', value: false },
    },
    Setting_Unit: {
      code: 'Setting_Unit',
      name: 'Danh sách Đơn vị tính',
      View: { name: 'Xem', code: 'Setting_Unit_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Unit_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Unit_Update', value: false },
    },
    Setting_Sale_Program: {
      code: 'Setting_Sale_Program',
      name: 'Chương trình bán hàng',
      View: { name: 'Xem', code: 'Setting_Sale_Program_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Sale_Program_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Sale_Program_Update', value: false },
    },
    Warehouse_Transfer: {
      code: 'Warehouse_Transfer',
      name: 'Chuyển kho',
      View: { name: 'Xem', code: 'Warehouse_Transfer_View', value: false },
      Add: { name: 'Thêm', code: 'Warehouse_Transfer_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Warehouse_Transfer_Update', value: false },
      Approve: { name: 'Duyệt', code: 'Warehouse_Transfer_Approve', value: false },
      Cancel: { name: 'Huỷ', code: 'Warehouse_Transfer_Cancel', value: false },
    },
    PaySlipShipper: {
      code: 'PaySlipShipper',
      name: 'Chi phí cho Shipper',
      Add: { name: 'Thêm', code: 'PaySlipShipper_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'PaySlipShipper_Update', value: false },
      View: { name: 'Xem', code: 'PaySlipShipper_View', value: false },
    },
    CollectOther: {
      code: 'CollectOther',
      name: 'Thu khác',
      Add: { name: 'Thêm', code: 'CollectOther_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'CollectOther_Update', value: false },
      View: { name: 'Xem', code: 'CollectOther_View', value: false },
    },
    WorkManagement: {
      code: 'WorkManagement',
      name: 'Quản lý công việc',
      View: { name: 'Xem', code: 'WorkManagement_View', value: false },
      Update: { name: 'Chỉnh sửa', code: 'WorkManagement_Update', value: false },
      Add: { name: 'Thêm', code: 'WorkManagement_Add', value: false },
    },
    VirtualWarehouse: {
      code: 'VirtualWarehouse',
      name: 'Phân kho ảo',
      View: { name: 'Xem', code: 'VirtualWarehouse_View', value: false },
      Update: { name: 'Chỉnh sửa', code: 'VirtualWarehouse_Update', value: false },
      Add: { name: 'Thêm', code: 'VirtualWarehouse_Add', value: false },
    },
    EmployeeRequestProduct: {
      code: 'EmployeeRequestProduct',
      name: 'Yêu cầu phân kho',
      View: { name: 'Xem', code: 'EmployeeRequestProduct_View', value: false },
      Update: { name: 'Chỉnh sửa', code: 'EmployeeRequestProduct_Update', value: false },
      Add: { name: 'Thêm', code: 'EmployeeRequestProduct_Add', value: false },
    },
    EmployeeRequestWarehouse: {
      code: 'EmployeeRequestWarehouse',
      name: 'Sale Admin Phân Kho',
      View: { name: 'Xem', code: 'EmployeeRequestWarehouse_View', value: false },
      Update: { name: 'Chỉnh sửa', code: 'EmployeeRequestWarehouse_Update', value: false },
      Add: { name: 'Thêm', code: 'EmployeeRequestWarehouse_Add', value: false },
    },
    EmployeeWarehousereRequestReturn: {
      code: 'Employee_Warehousere_Request_Return',
      name: 'Yêu cầu thu hồi kho',
      View: { name: 'Xem', code: 'Employee_Warehousere_Request_Return_View', value: false },
      Add: { name: 'Thêm', code: 'Employee_Warehousere_Request_Return_Add', value: false },
      Update: { name: 'Cập nhật', code: 'Employee_Warehousere_Request_Return_Update', value: false },
      Export: { name: 'Xuất Excel', code: 'Employee_Warehousere_Request_Return_Export', value: false },
    },
    Customer_Type: {
      code: 'Customer_Type',
      name: 'Loại khách hàng',
      View: { name: 'Xem', code: 'Customer_Type_View', value: false },
      Add: { name: 'Thêm', code: 'Customer_Type_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Customer_Type_Update', value: false },
    },
  },
  OrderStatus: {
    DonMoiTao: { code: 'DonMoiTao', name: 'Đơn mới tạo', description: 'Sale tạo đơn', color: 'blue' },
    GuiDonHang: { code: 'GuiDonHang', name: 'Gửi đơn hàng', description: 'Gửi đơn hàng xuống kho', color: 'darkblue' },
    KhoTiepNhan: {
      code: 'KhoTiepNhan',
      name: 'Kho tiếp nhận',
      description: 'Kho đã tiếp nhận đơn',
      color: 'blueviolet',
    },
    DonDangSoan: { code: 'DonDangSoan', name: 'Đơn đang soạn', description: 'Kho đang soạn đơn', color: 'yellowgreen' },
    DonDaSoan: {
      code: 'DonDaSoan',
      name: 'Đơn đã soạn',
      description: 'NV kho cập nhật trạng thái đã soạn đơn',
      color: '#1f1c43',
    },
    DonChoNhan: {
      code: 'DonChoNhan',
      name: 'Đơn đã nhận',
      description: 'Shipper nhận đơn',
      color: 'orchid',
    },
    DonChoPhat: {
      code: 'DonChoPhat',
      name: 'Đơn chờ phát',
      description: 'Shipper xác nhận đã lấy hàng',
      color: 'yellowgreen',
    },
    DonDangPhat: {
      code: 'DonDangPhat',
      name: 'Đơn đang phát',
      description: 'Shipper xác nhận sẽ phát hàng',
      color: 'green',
    },

    DonPhatLai: { code: 'DonPhatLai', name: 'Đơn phát lại', description: '', color: 'darkred' },
    DonHoanGoc: { code: 'DonHoanGoc', name: 'Đơn hoàn gốc', description: '', color: 'deeppink' },
    DonPhatThanhCong: { code: 'DonPhatThanhCong', name: 'Đơn phát thành công', description: '', color: 'orange' },
    DonHoanThanhCong: { code: 'DonHoanThanhCong', name: 'Đơn hoàn thành công', description: '', color: 'darkgreen' },
    DonHoanThanh: { code: 'DonHoanThanh', name: ' Đơn hoàn thành', description: '', color: 'darkorange' },
    DonHuy: { code: 'DonHuy', name: 'Đơn huỷ', description: '', color: 'red' },
  },
  PaymentStatus: {
    COD: { code: 'COD', name: 'Thu hộ ( COD )', description: 'Đơn thu hộ' },
    CongNo: { code: 'CongNo', name: 'Công nợ', description: 'Đơn nợ' },
    ChuyenKhoan: { code: 'ChuyenKhoan', name: 'Chuyển Khoản', description: 'Đơn nợ' },
  },
  ActionLogType: {
    CREATE: { code: 'CREATE', name: 'Thêm mới' },
    UPDATE: { code: 'UPDATE', name: 'Cập nhật' },
    UPDATE_ACTIVE: { code: 'UPDATE_ACTIVE', name: 'Cập nhật trạng thái' },
    IMPORT: { code: 'IMPORT', name: 'Nhập excel' },
    CANCEL: { code: 'CANCEL', name: 'Hủy' },
    APPROVE: { code: 'APPROVE', name: 'Duyệt' },
  },
  /** Loại hợp đồng điện tử */
  EContractTye: {
    PARTTIME: { code: 'PARTTIME', name: 'Cộng tác viên' },
    FULLTIME: { code: 'FULLTIME', name: 'Có thời hạn' },
  },

  EContractType: {
    COMPANY: { code: 'COMPANY', name: 'Doanh nghiệp' },
    PERSONAL: { code: 'PERSONAL', name: 'Cá nhân' },
  },

  EBusinessType: {
    LED_ADVERTISEMENT: { code: 'LED Advertisement', name: 'Quảng cáo LED' },
    SOCIAL_SECURITY_CARD: { code: 'Social Security Card', name: 'Thẻ An Sinh Bình Ổn' },
    TOURISM: { code: 'Tourism', name: 'Du lịch' },
    HEALTH: { code: 'Health', name: 'Sức khỏe' },
    SME360: { code: 'SME 360', name: 'SME 360' },
    FOOD: { code: 'FOOD', name: 'Thực phẩm nhân đạo' },
    RETAIL: { code: 'RETAIL', name: 'Mua lẻ' },
    EDUCATION: { code: 'EDUCATION', name: 'Giáo dục' },
  },
  // #region Phân quyền
  Role: {
    Report: {
      code: 'Report',
      name: 'Báo cáo',
      View: { name: 'Xem', code: 'Report_View', value: false },
    },
    Commission_Report: {
      code: 'Commission_Report',
      name: 'Báo cáo hoa hồng',
      View: { name: 'Xem', code: 'Commission_Report_View', value: false },
    },
    List_Transaction: {
      code: 'List_Transaction',
      name: 'Danh sách giao dịch',
      View: { name: 'Xem', code: 'List_Transaction_View', value: false },
      Add: { name: 'Thêm', code: 'List_Transaction_Add', value: false },
    },
    Card_Report: {
      code: 'Card_Report',
      name: 'Báo cáo bán thẻ',
      View: { name: 'Xem', code: 'Card_Report_View', value: false },
    },
    Tracking_Report: {
      code: 'Tracking_Report',
      name: 'Báo cáo tracking',
      View: { name: 'Xem', code: 'Tracking_Report_View', value: false },
    },
    Survey: {
      code: 'Survey',
      name: 'Khảo sát',
      View: { name: 'Xem', code: 'Survey_View', value: false },
    },
    //thống kê
    Survey_Statistics: {
      code: 'Survey_Statistics',
      name: 'Thống kê khảo sát',
      View: { name: 'Xem', code: 'Survey_Statistics_View', value: false },
    },
    //Phiếu trả lời khảo sát
    Survey_Answer: {
      code: 'Survey_Answer',
      name: 'Phiếu trả lời khảo sát',
      View: { name: 'Xem', code: 'Survey_Answer_View', value: false },
    },
    //Phiếu khảo sát
    Vote_Survey: {
      code: 'Vote_Survey',
      name: 'Phiếu khảo sát',
      View: { name: 'Xem', code: 'Vote_Survey_View', value: false },
      Add: { name: 'Thêm', code: 'Vote_Survey_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Vote_Survey_Update', value: false },
    },

    //Quản lý user khảo sát
    Survey_User: {
      code: 'Survey_User',
      name: 'Quản lý user khảo sát',
      View: { name: 'Xem', code: 'Survey_User_View', value: false },
      Add: { name: 'Thêm', code: 'Survey_User_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Survey_User_Update', value: false },
    },

    //thiết lập danh mục khảo sát
    Survey_Category: {
      code: 'Survey_Category',
      name: 'Thiết lập danh mục khảo sát',
      View: { name: 'Xem', code: 'Survey_Category_View', value: false },
      Add: { name: 'Thêm', code: 'Survey_Category_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Survey_Category_Update', value: false },
    },

    //thiết lập chủ đề khảo sát topic
    Survey_Topic: {
      code: 'Survey_Topic',
      name: 'Thiết lập chủ đề khảo sát',
      View: { name: 'Xem', code: 'Survey_Topic_View', value: false },
      Add: { name: 'Thêm', code: 'Survey_Topic_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Survey_Topic_Update', value: false },
    },
    Statistical: {
      code: 'Statistical',
      name: 'Thống kê',
      View: { name: 'Xem', code: 'Statistical_View', value: false },
    },
    Vote_Answer: {
      code: 'Vote_Answer',
      name: 'Phiếu trả lời',
      View: { name: 'Xem', code: 'Vote_Answer_View', value: false },
    },

    User_Management: {
      code: 'User_Management',
      name: 'Quản lý user',
      View: { name: 'Xem', code: 'User_Management_View', value: false },
    },
    ManagerCard: {
      code: 'ManagerCard',
      name: 'Quản lý thẻ',
      View: { name: 'Xem', code: 'ManagerCard_View', value: false },
    },
    ListCard: {
      code: 'ListCard',
      name: 'Danh sách thẻ',
      View: { name: 'Xem', code: 'ListCard_View', value: false },
      Add: { name: 'Thêm', code: 'ListCard_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'ListCard_Update', value: false },
    },
    Contract_Card: {
      code: 'Contract_Card',
      name: 'Hợp đồng bán thẻ',
      View: { name: 'Xem', code: 'Contract_Card_View', value: false },
    },
    Config_Card: {
      code: 'Config_Card',
      name: 'Cấu hình thẻ',
      View: { name: 'Xem', code: 'Config_Card_View', value: false },
    },
    Config_Type_Card: {
      code: 'Config_Type_Card',
      name: 'Cấu hình loại thẻ',
      View: { name: 'Xem', code: 'Config_Type_Card_View', value: false },
      Add: { name: 'Thêm', code: 'Config_Type_Card_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Config_Type_Card_Update', value: false },
    },
    Config_Period_Card: {
      code: 'Config_Period_Card',
      name: 'Cấu hình kỳ sử dụng',
      View: { name: 'Xem', code: 'Config_Period_Card_View', value: false },
      Add: { name: 'Thêm', code: 'Config_Period_Card_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Config_Period_Card_Update', value: false },
    },
    Config_Expiried_Card: {
      code: 'Config_Expiried_Card',
      name: 'Cấu hình hạn sử dụng',
      View: { name: 'Xem', code: 'Config_Expiried_Card_View', value: false },
      Add: { name: 'Thêm', code: 'Config_Expiried_Card_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Config_Expiried_Card_Update', value: false },
    },
    WareHouse: {
      code: ' WareHouse',
      name: 'Kho hàng',
      View: { name: 'Xem', code: 'WareHouse_View', value: false },
    },
    Inbound: {
      code: ' Inbound',
      name: 'Nhập kho',
      View: { name: 'Xem', code: 'Inbound_View', value: false },
    },
    Outbound: {
      code: ' Outbound',
      name: 'Xuất kho',
      View: { name: 'Xem', code: 'Outbound_View', value: false },
    },
    Inventory: {
      code: ' Inventory',
      name: 'Tồn kho',
      View: { name: 'Xem', code: 'Inventory_View', value: false },
    },
    Check_Inventory: {
      code: ' CheckInventory',
      name: 'Kiểm kho',
      View: { name: 'Xem', code: 'Check_Inventory_View', value: false },
    },
    Warehouse_Transfer: {
      code: 'Warehouse_Transfer',
      name: 'Chuyển kho',
      View: { name: 'Xem', code: 'Warehouse_Transfer_View', value: false },
    },
    Setting_Warehouse: {
      code: 'Setting_Warehouse',
      name: 'Danh sách kho',
      View: { name: 'Xem', code: 'Setting_Warehouse_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Warehouse_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Warehouse_Update', value: false },
    },
    PO_Management: {
      code: 'PO_Management',
      name: 'Quản lý đơn hàng',
      View: { name: 'Xem', code: 'PO_Management_View', value: false },
      Add: { name: 'Thêm', code: 'PO_Management_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'PO_Management_Update', value: false },
    },
    PO: {
      code: 'PO',
      name: 'Tạo PO',
      View: { name: 'Xem', code: 'PO_View', value: false },
      Add: { name: 'Thêm', code: 'PO_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'PO_Update', value: false },
    },
    List_PO: {
      code: 'List_PO',
      name: 'Danh sách PO',
      View: { name: 'Xem', code: 'List_PO_View', value: false },
      Add: { name: 'Thêm', code: 'List_PO_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'List_PO_Update', value: false },
    },
    Delivery: {
      code: 'Delivery',
      name: 'Phiếu giao nhận',
      View: { name: 'Xem', code: 'Delivery_View', value: false },
      Add: { name: 'Thêm', code: 'Delivery_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Delivery_Update', value: false },
    },
    Delivery_Child: {
      code: 'Delivery_Child',
      name: 'Phiếu giao nhận con',
      View: { name: 'Xem', code: 'Delivery_Child_View', value: false },
      Add: { name: 'Thêm', code: 'Delivery_Child_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Delivery_Child_Update', value: false },
    },
    Tracking: {
      code: 'Tracking',
      name: 'Tracking',
      View: { name: 'Xem', code: 'Tracking_View', value: false },
      Add: { name: 'Thêm', code: 'Tracking_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Tracking_Update', value: false },
    },

    Orders: {
      code: 'Orders',
      name: 'Đơn hàng',
      View: { name: 'Xem', code: 'Orders_View', value: false },
      Add: { name: 'Thêm', code: 'Orders_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Orders_Update', value: false },
    },
    Members: {
      code: 'Members',
      name: 'Thành viên',
      View: { name: 'Xem', code: 'Members_View', value: false },
      Add: { name: 'Thêm', code: 'Members_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Members_Update', value: false },
    },
    //contact
    Contact: {
      code: 'Contact',
      name: 'Tư vấn',
      View: { name: 'Xem', code: 'Contact_View', value: false },
      Add: { name: 'Thêm', code: 'Contact_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Contact_Update', value: false },
    },

    List_Customers: {
      code: 'List_Customers',
      name: 'Danh sách khách hàng',
      View: { name: 'Xem', code: 'List_Customers_View', value: false },
      Add: { name: 'Thêm', code: 'List_Customers_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'List_Customers_Update', value: false },
    },
    List_Collaborators: {
      code: 'List_Collaborators',
      name: 'Danh sách cộng tác viên',
      View: { name: 'Xem', code: 'List_Collaborators_View', value: false },
      Add: { name: 'Thêm', code: 'List_Collaborators_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'List_Collaborators_Update', value: false },
    },

    Withdrawal_Request: {
      code: 'Withdrawal_Request',
      name: 'Yêu cầu rút tiền',
      View: { name: 'Xem', code: 'Withdrawal_Request_View', value: false },
      Add: { name: 'Thêm', code: 'Withdrawal_Request_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Withdrawal_Request_Update', value: false },
    },

    Withdraw_Money: {
      code: 'Withdraw_Money',
      name: 'Rút tiền',
      View: { name: 'Xem', code: 'Withdraw_Money_View', value: false },
      Add: { name: 'Thêm', code: 'Withdraw_Money_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Withdraw_Money_Update', value: false },
    },
    Schedule: {
      code: 'Schedule',
      name: 'Bảng kê',
      View: { name: 'Xem', code: 'Schedule_View', value: false },
      Add: { name: 'Thêm', code: 'Schedule_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Schedule_Update', value: false },
    },
    Payment_Profile: {
      code: 'Payment_Profile',
      name: 'Hồ sơ thanh toán',
      View: { name: 'Xem', code: 'Payment_Profile_View', value: false },
      Add: { name: 'Thêm', code: 'Payment_Profile_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Payment_Profile_Update', value: false },
    },
    Payment_Request: {
      code: 'Payment_Request',
      name: 'Yêu cầu thanh toán',
      View: { name: 'Xem', code: 'Payment_Request_View', value: false },
      Add: { name: 'Thêm', code: 'Payment_Request_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Payment_Request_Update', value: false },
    },
    Warning: {
      code: 'Warning',
      name: 'Cảnh báo',
      View: { name: 'Xem', code: 'Warning_View', value: false },
      Add: { name: 'Thêm', code: 'Warning_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Warning_Update', value: false },
    },
    Payment_Warning: {
      code: 'Payment_Warning',
      name: 'Cảnh báo thanh toán',
      View: { name: 'Xem', code: 'Payment_Warning_View', value: false },
      Add: { name: 'Thêm', code: 'Payment_Warning_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Payment_Warning_Update', value: false },
    },
    Setting: {
      code: 'Setting',
      name: 'Thiết lập',
      View: { name: 'Xem', code: 'Setting_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Update', value: false },
    },

    //chuong trình khuyến mãi
    Setting_Promotion: {
      code: 'Setting_Promotion',
      name: 'Chương trình khuyến mãi',
      View: { name: 'Xem', code: 'Setting_Promotion_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Promotion_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Promotion_Update', value: false },
    },

    //Voucher
    Setting_Voucher: {
      code: 'Setting_Voucher',
      name: 'Voucher',
      View: { name: 'Xem', code: 'Setting_Voucher_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Voucher_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Voucher_Update', value: false },
    },

    //Danh sách Voucher
    Setting_Voucher_List: {
      code: 'Setting_Voucher_List',
      name: 'Danh sách Voucher',
      View: { name: 'Xem', code: 'Setting_Voucher_List_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Voucher_List_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Voucher_List_Update', value: false },
    },
    // cấu hình voucher
    Setting_Voucher_Config: {
      code: 'Setting_Voucher_Config',
      name: 'Cấu hình Voucher',
      View: { name: 'Xem', code: 'Setting_Voucher_Config_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Voucher_Config_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Voucher_Config_Update', value: false },
    },

    Setting_Permission: {
      code: 'Setting_Permission',
      name: 'Phân quyền',
      View: { name: 'Xem', code: 'Setting_Permission_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Permission_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Permission_Update', value: false },
    },
    Setting_Customer: {
      code: 'Setting_Customer',
      name: 'Danh sách khách hàng',
      View: { name: 'Xem', code: 'Setting_Customer_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Customer_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Customer_Update', value: false },
    },
    Setting_Product: {
      code: 'Setting_Product',
      name: 'Danh sách sản phẩm',
      View: { name: 'Xem', code: 'Setting_Product_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Product_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Product_Update', value: false },
    },
    Setup_Product: {
      code: 'Setup_Product',
      name: 'Thiết lập sản phẩm',
      View: { name: 'Xem', code: 'Setup_Product_View', value: false },
      Add: { name: 'Thêm', code: 'Setup_Product_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setup_Product_Update', value: false },
    },
    Setting_Combo: {
      code: 'Setting_Combo',
      name: 'Danh sách Combo',
      View: { name: 'Xem', code: 'Setting_Combo_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Combo_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Combo_Update', value: false },
    },
    Setting_Unit: {
      code: 'Setting_Unit',
      name: 'Danh sách Đơn vị tính',
      View: { name: 'Xem', code: 'Setting_Unit_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Unit_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Unit_Update', value: false },
    },
    Setting_Brand: {
      code: 'Setting_Brand',
      name: 'Danh sách thương hiệu',
      View: { name: 'Xem', code: 'Setting_Brand_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Brand_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Brand_Update', value: false },
    },
    Setting_Product_Group: {
      code: 'Setting_Product_Group',
      name: 'Danh sách nhóm sản phẩm',
      View: { name: 'Xem', code: 'Setting_Product_Group_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Product_Group_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Product_Group_Update', value: false },
    },
    Setting_Product_Category: {
      code: 'Setting_Product_Category',
      name: 'Danh mục sản phẩm',
      View: { name: 'Xem', code: 'Setting_Product_Category_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Product_Category_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Product_Category_Update', value: false },
    },
    Setting_Product_Type: {
      code: 'Setting_Product_Type',
      name: 'Loại sản phẩm',
      View: { name: 'Xem', code: 'Setting_Product_Type_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Product_Type_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Product_Type_Update', value: false },
    },
    Setting_Product_Price: {
      code: 'Setting_Product',
      name: 'Danh sách giá sản phẩm',
      View: { name: 'Xem', code: 'Setting_Product_Price_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Product_Price__Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Product_Price_Update', value: false },
    },
    Setting_Sale_Program: {
      code: 'Setting_Sale_Program',
      name: 'Chương trình khuyến mãi',
      View: { name: 'Xem', code: 'Setting_Sale_Program_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Sale_Program_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Sale_Program_Update', value: false },
    },
    Setting_Tax: {
      code: 'Setting_Tax',
      name: 'Khai báo mức thuế',
      View: { name: 'Xem', code: 'Setting_Tax_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Tax_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Tax_Update', value: false },
    },
    Setting_Geo: {
      code: 'Setting_Geo',
      name: 'Đơn vị hành chính',
      View: { name: 'Xem', code: 'Setting_Geo_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Geo_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Geo_Update', value: false },
    },
    Setting_Region: {
      code: 'Setting_Region',
      name: 'Thiết lập vùng',
      View: { name: 'Xem', code: 'Setting_Region_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Region_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Region_Update', value: false },
    },
    Setting_City: {
      code: 'Setting_City',
      name: 'Danh sách Tỉnh/Thành phố',
      View: { name: 'Xem', code: 'Setting_City_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_City_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_City_Update', value: false },
    },
    Setting_District: {
      code: 'Setting_District',
      name: 'Danh sách Quận/Huyện',
      View: { name: 'Xem', code: 'Setting_District_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_District_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_District_Update', value: false },
    },
    Setting_Ward: {
      code: 'Setting_Ward',
      name: 'Danh sách Phường/Xã',
      View: { name: 'Xem', code: 'Setting_Ward_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Ward_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Ward_Update', value: false },
    },
    Supply_Chain: {
      code: 'Supply_Chain',
      name: 'Chuỗi cung ứng',
      View: { name: 'Xem', code: 'Supply_Chain_View', value: false },
    },
    Setting_Supplier: {
      code: 'Setting_Supplier',
      name: 'Quản lý NCC, NPP, 3PL',
      View: { name: 'Xem', code: 'Setting_Supplier_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Supplier_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Supplier_Update', value: false },
    },
    Setting_Supply_Chain: {
      code: 'Setting_Supply_Chain',
      name: 'Phân bổ chuỗi cung ứng',
      View: { name: 'Xem', code: 'Setting_Supply_Chain_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Supply_Chain_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Supply_Chain_Update', value: false },
    },
    Setting_Company: {
      code: 'Setting_Company',
      name: 'Thiết lập công ty',
      View: { name: 'Xem', code: 'Setting_Company_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Company_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Company_Update', value: false },
    },
    Setting_Employee: {
      code: 'Setting_Employee',
      name: 'Danh sách nhân viên',
      View: { name: 'Xem', code: 'Setting_Employee_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Employee_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Employee_Update', value: false },
    },
    Setting_Department: {
      code: 'Setting_Department',
      name: 'Phòng ban',
      View: { name: 'Xem', code: 'Setting_Department_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Department_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Department_Update', value: false },
    },
    Setting_Partner: {
      code: 'Setting_Partner',
      name: 'Thiết lập đối tác',
      View: { name: 'Xem', code: 'Setting_Partner_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Partner_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Partner_Update', value: false },
    },
    Setting_Store: {
      code: 'Setting_Store',
      name: 'Thiết lập cửa hàng',
      View: { name: 'Xem', code: 'Setting_Store_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Store_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Store_Update', value: false },
    },
    Setting_Bonus: {
      code: 'Setting_Bonus',
      name: 'Thiết lập thưởng',
      View: { name: 'Xem', code: 'Setting_Bonus_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Bonus_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Bonus_Update', value: false },
    },
    Setting_Contract: {
      code: 'Setting_Contract',
      name: 'Thiết lập hợp đồng',
      View: { name: 'Xem', code: 'Setting_Contract_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Contract_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Contract_Update', value: false },
    },
    Setting_Display: {
      code: 'Setting_Display',
      name: 'Thiết lập tồn kho an toàn',
      View: { name: 'Xem', code: 'Setting_Display_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Display_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Display_Update', value: false },
    },
    Setting_Orther: {
      code: 'Setting_Orther',
      name: 'Thiết lập khác',
      View: { name: 'Xem', code: 'Setting_Orther_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Orther_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Orther_Update', value: false },
    },
    Setting_Bank: {
      code: 'Setting_Bank',
      name: 'Danh sách ngân hàng',
      View: { name: 'Xem', code: 'Setting_Bank_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Bank_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Bank_Update', value: false },
    },
    Setting_News_Category: {
      code: 'Setting_News_Category',
      name: 'Danh mục tin tức',
      View: { name: 'Xem', code: 'Setting_News_Category_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_News_Category_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_News_Category_Update', value: false },
    },
    Setting_News: {
      code: 'Setting_News',
      name: 'Danh sách tin tức',
      View: { name: 'Xem', code: 'Setting_News_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_News_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_News_Update', value: false },
    },
    Setting_String: {
      code: 'Setting_String',
      name: 'Thiết lập điều khoản app sale',
      View: { name: 'Xem', code: 'Setting_String_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_String_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_String_Update', value: false },
    },
    Setting_Media: {
      code: 'Setting_Media',
      name: 'Thiết lập media',
      View: { name: 'Xem', code: 'Setting_Media_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Media_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Media_Update', value: false },
    },
    Setting_Topic_Survey: {
      code: 'Setting_Topic_Survey',
      name: 'Thiết lập chủ đề khảo sát',
      View: { name: 'Xem', code: 'Setting_Topic_Survey_View', value: false },
      Add: { name: 'Thêm', code: 'Setting_Topic_Survey_Add', value: false },
      Update: { name: 'Chỉnh sửa', code: 'Setting_Topic_Survey_Update', value: false },
    },
  },
  // #endregion
}

export const transformer = {
  // entity to db
  to(value: any) {
    return value
  },
  // db to entity
  from(value: any) {
    return +value || 0
  },
}
