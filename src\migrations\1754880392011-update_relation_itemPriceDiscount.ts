import { MigrationInterface, QueryRunner } from 'typeorm'

export class updateRelationItemPriceDiscount1754880392011 implements MigrationInterface {
  name = 'updateRelationItemPriceDiscount1754880392011'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "item_price_discount" DROP CONSTRAINT "FK_660b18aa7402947e7d869fccf9c"`)
    await queryRunner.query(
      `ALTER TABLE "item_price_discount" ADD CONSTRAINT "FK_660b18aa7402947e7d869fccf9c" FOREIGN KEY ("itemId") REFERENCES "item"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "item_price_discount" DROP CONSTRAINT "FK_660b18aa7402947e7d869fccf9c"`)
    await queryRunner.query(
      `ALTER TABLE "item_price_discount" ADD CONSTRAINT "FK_660b18aa7402947e7d869fccf9c" FOREIGN KEY ("itemId") REFERENCES "item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }
}
