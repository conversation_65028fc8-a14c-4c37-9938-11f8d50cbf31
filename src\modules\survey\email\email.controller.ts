import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common'
import { EmailSendPasswordDto } from './dto'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { EmailService } from './email.service'

@ApiBearerAuth()
@ApiTags('Email')
@Controller('email')
export class EmailController {
  constructor(private readonly service: EmailService) {}

  @ApiOperation({ summary: 'G<PERSON>i mật khẩu qua email đăng ký tài khoản khi user quên mật khẩu' })
  @Post('send_password')
  public async sendPassword(@Body() data: EmailSendPasswordDto) {
    return await this.service.sendPassword(data)
  }
}
