import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional, IsUUID, IsEnum, IsDate, IsNotEmpty } from 'class-validator'
import { NSPo } from '../../../../constants'
import { PageRequest, PaginationDto } from '../../../../dto'
import { Type } from 'class-transformer'

export class ListPoDto extends PageRequest {
  @ApiPropertyOptional({ description: 'PO Code' })
  @IsOptional()
  code?: string

  @ApiPropertyOptional({ description: 'Người tạo' })
  @IsOptional()
  @IsUUID()
  createBy?: string

  @ApiPropertyOptional({ description: 'ID Nhà cung cấp của PO' })
  @IsOptional()
  @IsUUID()
  supplierId?: string

  @ApiPropertyOptional({ description: 'ID Vùng' })
  @IsOptional()
  @IsUUID()
  regionId?: string

  @ApiPropertyOptional({
    description: `Trạng thái ${Object.values(NSPo.EPoStatus).join(' | ')}`,
    enum: NSPo.EPoStatus,
    isArray: true,
    default: [NSPo.EPoStatus.NEWLYCREATED],
  })
  @IsOptional()
  @IsEnum(NSPo.EPoStatus, { each: true })
  status?: NSPo.EPoStatus[]

  @ApiPropertyOptional({ description: 'Từ ngày' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  dateFrom?: Date

  @ApiPropertyOptional({ description: 'Đến ngày' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  dateTo?: Date

  @ApiPropertyOptional({ description: 'Loại PO WITHCOMBO / WITHPRODUCT / OTHER' })
  @IsOptional()
  @IsEnum(NSPo.EPoType)
  purchaseOrderType?: NSPo.EPoType

  @ApiPropertyOptional({ description: 'Trạng thái duyệt' })
  @IsOptional()
  approveStatus: string

  @ApiPropertyOptional({ description: '' })
  @IsOptional()
  approverCurrentId: string

  @ApiPropertyOptional({ description: 'Ngày nhận hàng mong muốn' })
  @IsOptional()
  @Type(() => Date)
  deliveryDateFrom?: Date

  @ApiPropertyOptional({ description: 'Ngày nhận hàng mong muốn' })
  @IsOptional()
  @Type(() => Date)
  deliveryDateTo?: Date
}

export class ListPoReq extends PaginationDto {
  @ApiPropertyOptional({ description: 'PO Code' })
  @IsOptional()
  code?: string

  @ApiPropertyOptional({ description: 'Người tạo' })
  @IsOptional()
  @IsUUID()
  createBy?: string

  @ApiPropertyOptional({ description: 'ID Nhà cung cấp của PO' })
  @IsOptional()
  @IsUUID()
  supplierId?: string

  @ApiPropertyOptional({
    description: `Trạng thái ${Object.values(NSPo.EPoStatus).join(' | ')}`,
    enum: NSPo.EPoStatus,
    isArray: true,
    default: [NSPo.EPoStatus.NEWLYCREATED],
  })
  @IsOptional()
  @IsEnum(NSPo.EPoStatus, { each: true })
  status?: NSPo.EPoStatus[]

  @ApiPropertyOptional({ description: 'Từ ngày' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  dateFrom?: Date

  @ApiPropertyOptional({ description: 'Đến ngày' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  dateTo?: Date

  @ApiPropertyOptional({ description: 'Loại PO WITHCOMBO / WITHPRODUCT / OTHER' })
  @IsOptional()
  @IsEnum(NSPo.EPoType)
  purchaseOrderType?: NSPo.EPoType

  @ApiPropertyOptional({ description: 'Trạng thái duyệt' })
  @IsOptional()
  approveStatus: string

  @ApiPropertyOptional({ description: '' })
  @IsOptional()
  approverCurrentId: string
}

export class ListHistoryPoDto extends PaginationDto {
  @ApiProperty({ description: 'ID PO' })
  @IsUUID()
  @IsNotEmpty()
  purchaseOrderId: string

  @ApiProperty({ description: 'Username thao tác' })
  @IsOptional()
  createdByName?: string
}

export class DetailPurchaseOrderDto {
  @ApiProperty({ description: 'ID PO' })
  @IsUUID()
  @IsNotEmpty()
  id: string

  @ApiProperty({ description: 'ID Nhà phân phối' })
  @IsOptional()
  distributorId?: string
}

export class ListPOwithSODto {
  @ApiProperty({ description: 'ID PO' })
  @IsUUID()
  @IsNotEmpty()
  id: string

  @ApiProperty({ description: 'ID NPP' })
  @IsOptional()
  distributorId?: string
}

export class ListPoItemDto {
  @ApiProperty({ description: 'ID PO' })
  @IsUUID()
  @IsNotEmpty()
  purchaseOrderId: string

  @ApiProperty({ description: 'ID Nhà cung cấp' })
  @IsOptional()
  supplierId?: string

  //regionId
  @ApiProperty({ description: 'ID Vùng' })
  @IsOptional()
  regionId?: string
}
