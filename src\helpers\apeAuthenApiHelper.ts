import { HttpService } from '@nestjs/axios'

class ApeAuthenApiHelper {
  private url = process.env.APE_AUTHEN_API
  private httpService: HttpService

  constructor(httpService: HttpService) {
    this.httpService = httpService
  }
  async register(data: any) {
    return this.httpService.post(`${this.url}/api/public/account/register`, data)
  }
}

export const apeAuthenApiHelper = new ApeAuthenApiHelper(new HttpService())
