import { Injectable } from '@nestjs/common'
import {
  InboundCreateDto,
  IncreaseQuantityOrderDto,
  WarehouseCreateDto,
  WarehouseCreateExcelDto,
  WarehouseProductSafetyCreateDto,
  WarehouseProductSafetyImportDto,
  WarehouseUpdateDto,
  WarehouseUpdateIsActiveDto,
} from './dto'
import {
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  IMPORT_SUCCESS,
  NSOperational,
  NSPo,
  NSWarehouse,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
  enumData,
} from '../../../constants'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { EntityManager, Equal, ILike, In, Like, Raw } from 'typeorm'
import * as moment from 'moment'
import {
  InboundDetailEntity,
  WarehouseProductEntity,
  WarehouseProductDetailEntity,
  WarehouseEntity,
  ProductInventoryHistoryEntity,
  ItemEntity,
  ItemDetailEntity,
  WarehouseProductSafetyEntity,
} from '../../../entities'
import { v4 as uuidv4 } from 'uuid'
import { coreHelper } from '../../../helpers'
import { Request as IRequest } from 'express'
import {
  WarehouseProductDetailRepository,
  WarehouseRepository,
  ItemRepository,
  UnitRepository,
  ItemDetailRepository,
  WarehouseProductRepository,
  WarehouseProductSafetyEntityRepository,
  ItemComboRepository,
  CityRepository,
  OperationalAreaRepository,
  SupplierRepository,
  DistrictRepository,
  WardRepository,
} from '../../../repositories'
import { ProductExpiryDateDto } from '../../core/item/dto'
@Injectable()
export class WarehouseService {
  constructor(
    private readonly repo: WarehouseRepository,
    private readonly productRepo: ItemRepository,
    private readonly warehouseRepository: WarehouseRepository,
    private readonly unitRepo: UnitRepository,
    private readonly warehouseProductRepo: WarehouseProductRepository,
    private readonly warehouseProductDetailRepo: WarehouseProductDetailRepository,
    private readonly productDetailRepo: ItemDetailRepository,
    private readonly warehouseProductSafetyRepo: WarehouseProductSafetyEntityRepository,
    private readonly itemRepo: ItemRepository,
    private readonly itemComboRepo: ItemComboRepository,
    private readonly cityRepo: CityRepository,
    private readonly districtRepo: DistrictRepository,
    private readonly wardRepo: WardRepository,
    private readonly operationalAreaRepo: OperationalAreaRepository,
    private readonly supplierRepo: SupplierRepository,
  ) { }

  /** Tìm tồn kho của sản phẩm theo hạn sử dụng */
  async findWarehouseProductInventory(data: ProductExpiryDateDto): Promise<{ quantity: number }> {
    const checkWarehouse = await this.repo.findOne({ where: { id: data.warehouseId, isDeleted: false }, select: { id: true } })
    if (!checkWarehouse) throw new Error(`Không tìm thấy kho!`)

    const checkProduct = await this.productRepo.findOne({ where: { id: data.productId }, select: { id: true, unitId: true } })
    if (!checkProduct) throw new Error(`Không tìm thấy sản phẩm!`)

    const findUnit = await this.unitRepo.findOne({ where: { id: checkProduct.unitId, isDeleted: false } })
    if (!findUnit) throw new Error(`Sản phẩm chưa thiết lập đơn vị tính hoặc đơn vị tính đã bị ngưng hoạt động!`)
    let quantity: number = 0

    {
      const findProductDetail = await this.warehouseProductDetailRepo.find({
        where: {
          warehouseId: data.warehouseId,
          productId: data.productId,
          expiryDate: Equal(new Date(data.expiryDate)),
        },
      })
      for (let pd of findProductDetail) {
        quantity += +pd.quantity || 0
      }
    }
    return { quantity }
  }

  async find(data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.partnerId) whereCon.storeId = data.partnerId
    return await this.repo.find({ where: whereCon })
  }

  async findItem(data: any) {
    let dicItem: any = {}
    const rs: any = await this.warehouseProductRepo.find({ where: { warehouseId: data.warehouseId, isDeleted: false }, relations: { details: true } })

    if (rs.length > 0) {
      const lstProductId = rs.map((e) => e.productId)
      const lstItem = await this.productRepo.find({ where: { id: In(lstProductId), isDeleted: false } })
      for (const item of lstItem) {
        dicItem[item.id] = item
      }
    }
    for (const data of rs) {
      data.code = dicItem[data.productId]?.code
      data.name = dicItem[data.productId]?.name
      data.id = dicItem[data.productId]?.id
      data.isCombo = dicItem[data.productId]?.isCombo
    }
    return rs
  }

  async loadData(userlogin?: UserDto) {
    const whereCon: any = { isDeleted: false }
    if (userlogin?.type === enumData.UserType.PartnerEmp.code && userlogin?.lstStore && userlogin?.lstStore.length > 0) {
      const lstStoreId = userlogin?.lstStore.map((e) => e.id)
      whereCon.id = In(lstStoreId)
    }
    const rs = await this.repo.find({ where: whereCon, select: { id: true, code: true, name: true } })
    return rs
  }

  async createData(data: WarehouseCreateDto, req: IRequest, user?: UserDto): Promise<any> {
    if (data.cityId) {
      // let city: any = await authApiHelper.findOneCity(req, { id: data.cityId })
      // if (!city) throw new Error(` Tỉnh / Thành vừa chọn không tồn tại. Vui lòng kiểm tra lại`)
    }
    if (data.districtId) {
      // let district: any = await authApiHelper.findOneDistrict(req, { id: data.districtId })
      // if (!district) throw new Error(` Quận / Huyện vừa chọn không tồn tại. Vui lòng kiểm tra lại`)
    }

    if (data.wardId) {
      // let ward: any = await authApiHelper.findOneWard(req, { id: data.wardId })
      // if (!ward) throw new Error(` Phường / Xã vừa chọn không tồn tại. Vui lòng kiểm tra lại`)
    }

    if (data.isDefault === true) {
      await this.repo.update({ isDeleted: false }, { isDefault: false })
    } else {
      const feeOther = await this.repo.findOne({ where: { isDeleted: false, isDefault: true } })
      if (!feeOther) {
        data.isDefault = true
      }
    }

    if (data.storeId) {
      const sup = await this.supplierRepo.findOne({ where: { id: data.storeId } })
      // Kiểm tra loại supplier để tạo ra kho
      if (sup) {
        if (sup.is3PL) {
          data.type = NSWarehouse.EWarehouseType['3PL']
        }
      }
    }

    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new Error(`Mã kho ${checkCodeExist.code} đã tồn tại. Vui lòng kiểm tra lại`)

    const newEntity = this.repo.create({
      ...data,
    })
    newEntity.createdBy = user?.id
    const newWH = await this.repo.save(newEntity)

    const city = await this.cityRepo.find()
    if (data.configProvince) {
      for (let item of data.configProvince) {
        await this.operationalAreaRepo.save({
          areaId: item,
          areaCode: city?.find((x) => x.id === item)?.code,
          areaName: city?.find((x) => x.id === item)?.name,
          type: NSOperational.EOperationalAreaType.PROVINCE,
          mappingRefType: NSOperational.EAppliedObjectType.WAREHOUSE,
          mappingRefId: newWH.id,
        })
      }
    }

    return { message: 'Tạo mới thành công' }
  }

  async createDataWithArray(data: WarehouseCreateDto[], req: IRequest, user?: UserDto): Promise<any> {
    if (data.length < 1) throw new Error(`Dữ liệu không hợp lệ`);
    const cityCodes = data.map(item => item.cityCode);
    const districtCodes = data.map(item => item.districtCode);
    const wardCodes = data.map(item => item.wardCode);
    const cities = await this.cityRepo.find({ where: { code: In(cityCodes) } });
    const districts = await this.districtRepo.find({ where: { code: In(districtCodes) } });
    const wards = await this.wardRepo.find({ where: { code: In(wardCodes) } });
    const mappingCodeGeo = data.map(item => {
      // Từ cityCode, districtCode, wardCode lấy ra ID
      const city = cities.find(x => x.code === item.cityCode);
      // if (!city) {
      //   console.log(`City with code [${item.cityCode}] not found`);
      // }
      const district = districts.find(x => x.code === item.districtCode);
      // if (!district) {
      //   console.log(`District with code [${item.districtCode}] not found`);
      // }
      const ward = wards.find(x => x.code === item.wardCode);
      // if (!ward) {
      //   console.log(`Ward with code [${item.wardCode}] not found`);
      // }
      return {
        ...item,
        cityId: city?.id,
        districtId: district?.id,
        wardId: ward?.id,
      };
    });
    const result = await this.warehouseRepository.save(mappingCodeGeo);
    return { message: 'Tạo mới thành công', totalResult: result.length }
  }

  //deleteDataWithArray
  async deleteDataWithArray(data: WarehouseCreateDto[], req: IRequest, user?: UserDto): Promise<any> {
    if (data.length < 1) throw new Error(`Dữ liệu không hợp lệ`);
    const codes = data.map(item => item.code);
    await this.warehouseRepository.delete({
      code: In(codes)
    });
    return { message: 'Xóa thành công' }
  }

  async updateData(data: WarehouseUpdateDto, req: IRequest, user?: UserDto): Promise<any> {
    if (data.cityId) {
      let city: any = await this.cityRepo.findOne({ where: { id: data.cityId } })
      if (!city) throw new Error(` Tỉnh / Thành vừa chọn không tồn tại. Vui lòng kiểm tra lại`)
    }
    if (data.districtId) {
      let district: any = await this.districtRepo.findOne({ where: { id: data.districtId } })
      if (!district) throw new Error(` Quận / Huyện vừa chọn không tồn tại. Vui lòng kiểm tra lại`)
    }

    if (data.wardId) {
      let ward: any = await this.wardRepo.findOne({ where: { id: data.wardId } })
      if (!ward) throw new Error(` Phường / Xã vừa chọn không tồn tại. Vui lòng kiểm tra lại`)
    }

    if (data.storeId) {
      const sup = await this.supplierRepo.findOne({ where: { id: data.storeId } })
      // Kiểm tra loại supplier để tạo ra kho
      if (sup) {
        if (sup.is3PL) {
          data.type = NSWarehouse.EWarehouseType['3PL']
        }
      }
    }

    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.updatedBy = user?.id
    entity.name = data.name
    entity.description = data.description
    entity.cbm = data.cbm
    entity.length = data.length
    entity.width = data.width
    entity.address = data.address
    entity.cityId = data.cityId
    entity.districtId = data.districtId
    entity.wardId = data.wardId
    entity.height = data.height
    entity.storeId = data.storeId
    entity.type = data.type
    await this.repo.save(entity)

    if (data.isDefault === true) {
      await this.repo.update({}, { isDefault: false })
      await this.repo.update(entity.id, { isDefault: true })
    }

    if (data.configProvince) {
      await this.operationalAreaRepo.delete({
        mappingRefId: data.id,
        mappingRefType: NSOperational.EAppliedObjectType.WAREHOUSE,
        type: NSOperational.EOperationalAreaType.PROVINCE,
      })
      const city = await this.cityRepo.find()
      const lstCity = data.configProvince.map((item) => {
        const findCity = city.find((x) => x.id === item)
        return findCity
      })
      for (let item of lstCity) {
        await this.operationalAreaRepo.save({
          areaId: item.id,
          areaCode: item.code,
          areaName: item.name,
          type: NSOperational.EOperationalAreaType.PROVINCE,
          mappingRefType: NSOperational.EAppliedObjectType.WAREHOUSE,
          mappingRefId: data.id,
        })
      }
    }

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  async pagination(data: PaginationDto, req: IRequest, userlogin?: UserDto) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.storeId) whereCon.storeId = data.where.storeId
    if (data.where.cityId) whereCon.cityId = data.where.cityId
    if (data.where.districtId) whereCon.districtId = data.where.districtId
    if (data.where.wardId) whereCon.wardId = data.where.wardId
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    // if (userlogin?.type === enumData.UserType.PartnerEmp.code && userlogin?.lstStore && userlogin?.lstStore.length > 0) {
    //   const lstStoreId = userlogin?.lstStore.map((e) => e.id)
    //   whereCon.storeId = In(lstStoreId)
    // }
    let res: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
    if (res[0].length === 0) return []
    const dictCity: any = {}
    {
      // const lstId = coreHelper.selectDistinct(res[0], 'cityId')
      // const lstCity: any = await authApiHelper.findCity(req, { lstId })
      // lstCity.forEach((c) => (dictCity[c.id] = c.name))
    }
    const dictDistrict: any = {}
    {
      // const lstId = coreHelper.selectDistinct(res[0], 'districtId')
      // const lstDistrict: any = await authApiHelper.findDistrict(req, { lstId })
      // lstDistrict.forEach((c) => (dictDistrict[c.id] = c.name))
    }

    const dictWard: any = {}
    {
      // const lstId = coreHelper.selectDistinct(res[0], 'wardId')
      // const lstWard: any = await authApiHelper.findWard(req, { lstId })
      // lstWard.forEach((c) => (dictWard[c.id] = c.name))
    }

    for (let item of res[0]) {
      item.cityName = dictCity[item.cityId]
      item.districtName = dictDistrict[item.districtId]
      item.wardName = dictWard[item.wardId]
    }

    return res
  }

  async updateIsDelete(data: { id: string }, user?: UserDto) {
    const entity: any = await this.repo.findOne({ where: { id: data.id }, relations: { products: true } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.__products__ && entity.__products__.length > 0) {
      for (let item of entity.__products__) {
        if (item.quantity > 0) {
          throw new Error(`Kho đang tồn tại sản phẩm. Không thể ngưng hoạt động vui lòng thử lại`)
        }
      }
    }
    entity.isDeleted = !entity.isDeleted
    await this.repo.update(entity.id, { isDeleted: entity.isDeleted, updatedBy: user?.id })

    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  /** Tăng tồn kho của kho vật lý (hàm này thuộc trans hàm gọi nó) */
  async increaseInventory(trans: EntityManager, user: UserDto, data: { warehouseId: string; lstDetail: InboundDetailEntity[] }) {
    const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
    const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)
    const productInventoryHistoryRepo = trans.getRepository(ProductInventoryHistoryEntity)
    const repo = trans.getRepository(ItemEntity)
    const detailRepo = trans.getRepository(ItemDetailEntity)
    const dicProduct: any = {}
    for (const detail of data.lstDetail) {
      // lấy sản phẩm trong kho từ cache nếu k có thì lấy từ db, nếu lại k có thì tạo mới
      let warehouseProduct = dicProduct[detail.productId]
      if (!warehouseProduct) {
        warehouseProduct = await warehouseProductRepo.findOne({ where: { warehouseId: data.warehouseId, productId: detail.productId } })
      }
      if (!warehouseProduct) {
        const warehouseProductNew = new WarehouseProductEntity()
        warehouseProductNew.warehouseId = data.warehouseId
        warehouseProductNew.productId = detail.productId
        warehouseProductNew.quantity = 0
        warehouseProductNew.createdAt = new Date()
        warehouseProductNew.createdBy = user?.id
        warehouseProductNew.id = uuidv4()
        // lưu db
        await warehouseProductRepo.insert(warehouseProductNew)
        warehouseProduct = warehouseProductNew
      }
      // lưu cache
      dicProduct[detail.productId] = warehouseProduct

      const quantityOld = +warehouseProduct.quantity
      const quantityNew = quantityOld + +detail.totalQuantity
      // cập nhật cache
      warehouseProduct.quantity = quantityNew
      await warehouseProductRepo.update(warehouseProduct.id, { quantity: quantityNew })

      // lấy chi tiết sản phẩm trong kho từ cache nếu k có thì lấy từ db, nếu lại k có thì tạo mới
      const dateStr = moment(detail.expiryDate).format('YYYY-MM-DD')
      let warehouseProductDetail = dicProduct[detail.productId + dateStr]
      if (!warehouseProductDetail) {
        warehouseProductDetail = await warehouseProductDetailRepo.findOne({
          where: { warehouseId: data.warehouseId, productId: detail.productId, expiryDate: Equal(detail.expiryDate) },
        })
      }
      if (!warehouseProductDetail) {
        const warehouseProductDetailNew = new WarehouseProductDetailEntity()
        warehouseProductDetailNew.warehouseId = data.warehouseId
        warehouseProductDetailNew.productId = detail.productId
        warehouseProductDetailNew.warehouseProductId = warehouseProduct.id
        warehouseProductDetailNew.quantity = 0
        warehouseProductDetailNew.manufactureDate = detail.manufactureDate
        warehouseProductDetailNew.expiryDate = detail.expiryDate
        warehouseProductDetailNew.createdAt = new Date()
        warehouseProductDetailNew.createdBy = user?.id
        warehouseProductDetailNew.id = uuidv4()
        // lưu db
        await warehouseProductDetailRepo.insert(warehouseProductDetailNew)
        warehouseProductDetail = warehouseProductDetailNew
      }
      // lưu cache
      dicProduct[detail.productId + dateStr] = warehouseProductDetail

      const quantityDetailNew = +warehouseProductDetail.quantity + +detail.totalQuantity
      // cập nhật cache
      warehouseProductDetail.quantity = quantityDetailNew
      await warehouseProductDetailRepo.update(warehouseProductDetail.id, { quantity: quantityDetailNew })

      // lưu lịch sử
      const his = new ProductInventoryHistoryEntity()
      his.warehouseId = data.warehouseId
      his.warehouseProductId = warehouseProduct.id
      his.warehouseProductDetailId = warehouseProductDetail.id
      his.productId = detail.productId
      his.inboundId = detail.inboundId
      his.inboundDetailId = detail.id
      his.quantity = quantityOld
      his.quantityNew = quantityNew
      his.description = `Duyệt PNK [${detail.code}]<br>Nhập thêm: ${detail.totalQuantity}<br>HSD ${dateStr}`
      his.createdAt = new Date()
      his.createdBy = user?.id
      await productInventoryHistoryRepo.insert(his)
    }

    for (const detail of data.lstDetail) {
      const product = dicProduct[detail.productId]
      if (!product) throw new Error(`Sản phẩm ${detail.productName} không còn tồn tại!`)

      const quantityOld = +product.quantity
      const quantityNew = quantityOld + +detail.totalQuantity

      await repo.update(product.id, { quantity: quantityNew, expiryDate: detail.expiryDate, createdAt: new Date(), createdBy: user?.id })
      // cập nhật cache
      product.quantity = quantityNew

      // lấy chi tiết sản phẩm trong kho từ cache nếu k có thì lấy từ db, nếu lại k có thì tạo mới
      const dateStr = moment(detail.expiryDate).format('YYYY-MM-DD')
      let productDetail = dicProduct[detail.productId + dateStr]
      if (!productDetail) {
        productDetail = await detailRepo.findOne({
          where: { itemId: detail.productId, expiryDate: Equal(detail.expiryDate) },
        })
      }
      if (!productDetail) {
        const productDetailNew = new ItemDetailEntity()
        productDetailNew.itemId = detail.productId
        productDetailNew.quantity = 0
        productDetailNew.manufactureDate = detail.manufactureDate
        productDetailNew.expiryDate = detail.expiryDate
        productDetailNew.createdAt = new Date()
        productDetailNew.createdBy = user?.id
        productDetailNew.id = uuidv4()
        // lưu db
        await detailRepo.insert(productDetailNew)
        productDetail = productDetailNew
      }
      // lưu cache
      dicProduct[detail.productId + dateStr] = productDetail

      const quantityDetailNew = +productDetail.quantity + +detail.totalQuantity
      // cập nhật cache
      productDetail.quantity = quantityDetailNew
      await detailRepo.update(productDetail.id, { quantity: quantityDetailNew })
    }
  }

  async updateLockOrder(data: { warehouseId?: string; lstDetail: any[] }, user?: UserDto) {
    return await this.repo.manager.transaction('SERIALIZABLE', async (trans) => {
      const warehouseRepo = trans.getRepository(WarehouseEntity)
      const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)

      const checkWh = await warehouseRepo.findOne({ where: { id: data.warehouseId, isDeleted: false } })
      if (!checkWh) throw new Error(`Kho ${checkWh.name} không còn tồn tại!`)

      for (const detail of data.lstDetail) {
        const whProduct = await warehouseProductRepo.findOne({ where: { productId: detail.productId, isDeleted: false } })
        if (!whProduct) throw new Error(`Sản phẩm ${detail.productName} không còn tồn tại!`)

        const qValid = whProduct.quantity - whProduct.quantityLock
        if (detail.quantity > 0 && detail.quantity > qValid) {
          throw new Error('Số lượng tồn sản phẩm [ ' + detail.productName + ' ] không đủ. Vui lòng kiểm tra lại !')
        }
        const quantityOld = +whProduct.quantityLock
        const quantityNew = quantityOld + +detail.quantity
        await warehouseProductRepo.update(whProduct.id, { quantityLock: quantityNew, createdAt: new Date(), createdBy: user?.id })

        const pdDetail = await warehouseProductDetailRepo.findOne({
          where: { productDetailId: detail.productDetailId, isDeleted: false, productId: detail.productId, warehouseProductId: whProduct.id },
        })
        if (!pdDetail)
          throw new Error(
            `Sản phẩm ${detail.productName} có hạn sử dụng ${moment(detail.expiryDate).format(
              'YYYY-MM-DD',
            )} không còn tồn tại.  Vui lòng kiểm tra lại `,
          )

        const qValidDetail = pdDetail.quantity - +pdDetail.quantityLock
        if (detail.quantity > 0 && detail.quantity > qValidDetail) {
          throw new Error('Số lượng tồn sản phẩm [ ' + detail.productName + ' ] không đủ. Vui lòng kiểm tra lại !')
        }
        const quantityDetailNew = +pdDetail.quantityLock + +detail.quantity
        pdDetail.quantityLock = quantityDetailNew
        await warehouseProductDetailRepo.update(pdDetail.id, { quantityLock: quantityDetailNew })
      }
      return { message: UPDATE_SUCCESS }
    })
  }

  async updateCancelLockOrder(data: { warehouseId?: string; lstDetail: any[] }, user?: UserDto) {
    return await this.repo.manager.transaction('SERIALIZABLE', async (trans) => {
      const warehouseRepo = trans.getRepository(WarehouseEntity)
      const warehouseProductRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseProductDetailRepo = trans.getRepository(WarehouseProductDetailEntity)

      const checkWh = await warehouseRepo.findOne({ where: { id: data.warehouseId, isDeleted: false } })
      if (!checkWh) throw new Error(`Kho ${checkWh.name} không còn tồn tại!`)

      for (const detail of data.lstDetail) {
        const whProduct = await warehouseProductRepo.findOne({ where: { productId: detail.productId, isDeleted: false, warehouseId: checkWh.id } })
        if (!whProduct) throw new Error(`Sản phẩm ${detail.productName} không còn tồn tại!`)
        const pdDetail = await warehouseProductDetailRepo.findOne({
          where: { productDetailId: detail.productDetailId, isDeleted: false, productId: detail.productId, warehouseId: checkWh.id },
        })
        if (!pdDetail)
          throw new Error(
            `Sản phẩm ${detail.productName} có hạn sử dụng ${moment(detail.expiryDate).format(
              'YYYY-MM-DD',
            )} không còn tồn tại.  Vui lòng kiểm tra lại `,
          )
        const quantityOld = +whProduct.quantityLock
        const quantityNew = quantityOld - +detail.quantity
        await warehouseProductRepo.update(whProduct.id, { quantityLock: quantityNew, createdAt: new Date(), createdBy: user?.id })
        const quantityDetailNew = +pdDetail.quantityLock - +detail.quantity
        pdDetail.quantityLock = quantityDetailNew
        await warehouseProductDetailRepo.update(pdDetail.id, { quantityLock: quantityDetailNew })
      }
      return { message: UPDATE_SUCCESS }
    })
  }

  /** Chi tiết kho */
  public async findDetail(data: WarehouseUpdateIsActiveDto, req: IRequest) {
    let res: any

    if (data.isDownloadExcel === true) {
      res = await this.repo.findOne({
        where: { id: data.id, isDeleted: false },
        relations: { products: { details: true } },
        order: { products: { quantity: 'ASC' } },
      })
    } else {
      res = await this.repo.findOne({
        where: { id: data.id, isDeleted: false },
        relations: { products: { details: true }, inbounds: true, outbounds: true, productSafeties: true },
        order: { products: { quantity: 'ASC' } },
      })
    }

    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)
    if (res.cityId) {
      // let city: any = await authApiHelper.findOneCity(req, { id: res.cityId })
      // res.cityName = city?.name
    }
    if (res.districtId) {
      // let district: any = await authApiHelper.findOneDistrict(req, { id: res.districtId })
      // res.districtName = district?.name
    }

    if (res.wardId) {
      // let ward: any = await authApiHelper.findOneWard(req, { id: res.wardId })
      // res.wardName = ward?.name
    }

    const lstSafety = res.__productSafeties__ || []
    const mapSafety = lstSafety.convertToMap((s) => s.productId)

    let lstDetail = await res.__products__
    for (let item of lstDetail) {
      const productObj = await this.productRepo.findOne({ where: { id: item.productId } })
      item.productName = productObj?.name
      item.productCode = productObj?.code
      item.lstDetail = await item.__details__
      for (let i of item.lstDetail) {
        let dateHienTai = moment(new Date()).format('YYYY-MM-DD')
        let datehetHan = moment(i.expiryDate || new Date()).format('YYYY-MM-DD')
        let dateSanXuat = moment(i.manufactureDate || new Date()).format('YYYY-MM-DD')
        let rsDateHetHan = new Date(datehetHan)
        let rsDateSanXuat = new Date(dateSanXuat)
        let rsDateHienTai = new Date(dateHienTai)
        let TimeSanXuatHetHan = rsDateHetHan.getTime() - rsDateSanXuat.getTime()
        let totalTimeSanXuatHetHan = TimeSanXuatHetHan / (1000 * 3600 * 24)
        let DateHienTaiCanTim = rsDateHienTai.getTime() - rsDateSanXuat.getTime()
        let TimeCanTim = DateHienTaiCanTim / (1000 * 3600 * 24)
        let c = totalTimeSanXuatHetHan - TimeCanTim
        let phamTram = (c / totalTimeSanXuatHetHan) * 100
        i.phamTram = phamTram
      }
      item.expand = false

      item.quantityMinSafety = mapSafety.get(item.productId)?.quantityMin
      item.quantityMaxSafety = mapSafety.get(item.productId)?.quantityMax

      delete item.__product__
      delete item.__details__
    }
    if (data.isDownloadExcel === false) {
      let lstInbound = await res.__inbounds__
      let lstOutbound = await res.__outbounds__
      res.lstInbound = lstInbound
      res.lstOutbound = lstOutbound
    }

    res.lstDetail = lstDetail

    // Bổ sung thêm Operational Area
    const lstProvinceOperationalArea = await this.operationalAreaRepo.find({
      where: {
        mappingRefId: res.id,
        mappingRefType: NSOperational.EAppliedObjectType.WAREHOUSE,
        type: NSOperational.EOperationalAreaType.PROVINCE,
      },
      select: ['areaId', 'areaName'],
    })
    res['configProvince'] = lstProvinceOperationalArea.map((item) => ({ id: item.areaId, name: item.areaName }))

    // const createdByName = await authApiHelper.getCreatedByName(req, res.createdBy)
    // res.createdByName = createdByName
    delete res.__products__
    delete res.__inbounds__
    delete res.__outbounds__
    delete res.__productSafeties__
    return res
  }

  async findOne(data: FilterOneDto) {
    return await this.repo.findOneBy({ id: data.id })
  }

  async loadDataWarehouse(data: PaginationDto, userlogin?: UserDto) {
    let whereCon: any = {}
    whereCon.isDeleted = false
    if (data.where.warehouseId) whereCon.id = data.where.warehouseId
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.storeId) {
      const warehouse = await this.warehouseRepository.findOne({ where: { storeId: data.where.storeId } })
      if (warehouse) {
        whereCon.id = warehouse.id
      }
    }
    if (userlogin?.type === enumData.UserType.PartnerEmp.code && userlogin?.lstStore && userlogin?.lstStore.length > 0) {
      const lstStoreId = userlogin?.lstStore.map((e) => e.id)
      const lstWarehouse = await this.warehouseRepository.find({ where: { storeId: In(lstStoreId) } })
      if (lstWarehouse.length > 0) {
        const lstWarehouseId = lstWarehouse.map((e) => e.id)
        if (whereCon.warehouseId) {
          lstWarehouseId.push(whereCon.warehouseId)
          whereCon.warehouseId = In(lstWarehouseId)
        } else {
          whereCon.warehouseId = In(lstWarehouseId)
        }
      }
    }

    if (data.where.productId) {
      const lstDetail = await this.warehouseProductDetailRepo.find({ where: { productId: data.where.productId } })
      if (lstDetail.length === 0) return [[], 0]
      const lstdId = lstDetail.map((x) => x.warehouseProductId)
      whereCon.id = In(lstdId)
    }

    if (data.where.expiryDate && data.where.expiryDate.length > 0) {
      whereCon.expiryDate = Raw(
        (alias) =>
          `DATE(${alias}) BETWEEN DATE("${moment(data.where.expiryDate[0]).format('YYYY-MM-DD')}") AND DATE("${moment(
            data.where.expiryDate[1],
          ).format('YYYY-MM-DD')}")`,
      )
    }

    if (data.where.manufactureDate && data.where.manufactureDate.length > 0) {
      const lstDetail = await this.warehouseProductDetailRepo.find({
        where: {
          manufactureDate: Raw(
            (alias) =>
              `DATE(${alias}) BETWEEN DATE("${moment(data.where.manufactureDate[0]).format('YYYY-MM-DD')}") AND DATE("${moment(
                data.where.manufactureDate[1],
              ).format('YYYY-MM-DD')}")`,
          ),
        },
      })
      if (lstDetail.length === 0) return [[], 0]
      const lstdId = lstDetail.map((x) => x.warehouseProductId)
      whereCon.id = In(lstdId)
    }

    if (data.where.lotNumber) {
      const lstDetail = await this.productDetailRepo.find({ where: { lotNumber: Like(`%${data.where.lotNumber}%`) } })
      if (lstDetail.length === 0) return [[], 0]
      const lstdId = lstDetail.map((x) => x.itemId)

      const lstDetailWH = await this.warehouseProductDetailRepo.find({ where: { productId: In(lstdId) } })
      if (lstDetailWH.length === 0) return [[], 0]
      const lstdIdWh = lstDetailWH.map((x) => x.warehouseProductId)
      whereCon.id = In(lstdIdWh)
    }

    if (data.where.lotNumber) {
      const lstDetail = await this.productRepo.find({ where: { brandId: Like(`%${data.where.brandId}%`) } })
      if (lstDetail.length === 0) return [[], 0]
      const lstdId = lstDetail.map((x) => x.id)

      const lstDetailWH = await this.warehouseProductDetailRepo.find({ where: { productId: In(lstdId) } })
      if (lstDetailWH.length === 0) return [[], 0]
      const lstdIdWh = lstDetailWH.map((x) => x.warehouseProductId)
      whereCon.id = In(lstdIdWh)
    }

    let result: any = await this.repo.find({
      where: whereCon,
      relations: { products: true },
    })

    if (result.length == 0) return []

    for (let item of result) {
      const lstDetail = await item.__products__
      item.quantity = lstDetail.reduce((sum, current) => sum + Number(current.quantity), 0)
      delete item.__products__
    }

    return result
  }

  async createDataExcel(data: WarehouseCreateExcelDto[], req: IRequest, user?: UserDto) {
    await this.repo.manager.transaction(async (trans) => {
      const lstCityCode = data.mapAndDistinct((item) => item.cityCode)
      const lstDistrictCode = data.mapAndDistinct((item) => item.districtCode)
      const lstWardCode = data.mapAndDistinct((item) => item.wardCode)
      // const cities: any = await authApiHelper.findCity(req, { lstCode: lstCityCode })
      // const districts: any = await authApiHelper.findDistrict(req, { lstCode: lstDistrictCode })
      // const wards: any = await authApiHelper.findWard(req, { lstCode: lstWardCode })
      const cities = []
      const districts = []
      const wards = []

      const warehouseRepo = trans.getRepository(WarehouseEntity)

      const dicCode: any = {}
      {
        const lstCode = data.mapAndDistinct((item) => item.code)
        const locations: any[] = await warehouseRepo.find({
          where: { code: In(lstCode) },
          select: { id: true, code: true },
        })
        locations.forEach((c) => (dicCode[c.code] = c))
      }

      const cityMap = new Map(cities.map((c) => [c.code, c]))
      const districtMap = new Map(districts.map((d) => [d.code, d]))
      const wardMap = new Map(wards.map((w) => [w.code, w]))

      const lstWardhouseNew: WarehouseEntity[] = []
      const dicCodeFile: any = {}
      for (const [idx, item] of data.entries()) {
        if (dicCodeFile[item.code]) throw new Error(`[ Dòng ${idx + 2} - Mã Kho [${item.code}] trùng với dòng ${dicCodeFile[item.code]} ]`)
        if (dicCode[item.code]) throw new Error(`[ Dòng ${idx + 2} - Mã Kho [${item.code}] đã được sử dụng ]`)

        let cityId = null
        // if (item.cityCode) {
        //   const city: any = cityMap.get(item.cityCode)
        //   if (!city) throw new Error(`[ Dòng ${idx + 2} - Mã Tỉnh/  Thành phố [${item.cityCode}] không tồn tại ]`)
        //   cityId = city.id
        // }

        let districtId = null
        // if (item.districtCode) {
        //   const district: any = districtMap.get(item.districtCode)
        //   if (!district) throw new Error(`[ Dòng ${idx + 2} - Mã Quận / Huyện [${item.districtCode}] không tồn tại ]`)
        //   districtId = district.id
        // }

        let wardId = null
        // if (item.wardCode) {
        //   const ward: any = wardMap.get(item.wardCode)
        //   if (!ward) throw new Error(`[ Dòng ${idx + 2} - Mã Xã / Phường [${item.wardCode}] không tồn tại ]`)
        //   wardId = ward.id
        // }

        if (item.isDefault === true) {
          await warehouseRepo.update({ isDeleted: false }, { isDefault: false })
        } else {
          const feeOther = await warehouseRepo.findOne({ where: { isDeleted: false, isDefault: true } })
          if (!feeOther) {
            item.isDefault = true
          }
        }

        const newLocation = warehouseRepo.create({
          ...item,
          cityId: cityId,
          districtId: districtId,
          wardId: wardId,
          createdAt: new Date(),
          createdBy: user?.id,
        })
        lstWardhouseNew.push(newLocation)

        dicCodeFile[item.code] = idx + 1
      }
      await warehouseRepo.insert(lstWardhouseNew)
    })

    return { message: IMPORT_SUCCESS }
  }

  async paginationInventory(data: PaginationDto, req: IRequest) {
    let whereCon: any = {}
    if (data.where.productId) {
      const lstDetail = await this.warehouseProductDetailRepo.find({ where: { productId: data.where.productId } })
      if (lstDetail.length === 0) return [[], 0]
      const lstdId = lstDetail.map((x) => x.warehouseId)
      whereCon.id = In(lstdId)
    }

    if (data.where.name) whereCon.name = ILike(`%${data.where.name}%`)
    if (data.where.cityId) whereCon.cityId = data.where.cityId
    if (data.where.districtId) whereCon.districtId = data.where.districtId
    if (data.where.wardId) whereCon.wardId = data.where.wardId
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.warehouseId) whereCon.id = data.where.warehouseId
    if (data.where.storeId) {
      const warehouse = await this.warehouseRepository.find({ where: { storeId: data.where.storeId } })
      if (warehouse.length > 0) {
        whereCon.id = In(warehouse.map((item) => item.id))
      }
    }
    if (data.where.type) whereCon.type = NSWarehouse.EWarehouseType[data.where.type]
    whereCon.products = {}
    // whereCon.products.product = {}
    if (data.where.brandId) {
      whereCon.products.product.brandId = Like(`%${data.where.brandId}%`)
    }

    if (data.where.type) whereCon.type = data.where.type

    let res: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: { products: { details: true } },
    })
    if (res[0].length === 0) return []
    const dictCity: any = {}
    {
      // const lstId = coreHelper.selectDistinct(res[0], 'cityId')
      // const lstCity: any = await authApiHelper.findCity(req, { lstId })
      // lstCity.forEach((c) => (dictCity[c.id] = c.name))
    }
    const dictDistrict: any = {}
    {
      // const lstId = coreHelper.selectDistinct(res[0], 'districtId')
      // const lstDistrict: any = await authApiHelper.findDistrict(req, { lstId })
      // lstDistrict.forEach((c) => (dictDistrict[c.id] = c.name))
    }

    const dictWard: any = {}
    {
      // const lstId = coreHelper.selectDistinct(res[0], 'wardId')
      // const lstWard: any = await authApiHelper.findWard(req, { lstId })
      // lstWard.forEach((c) => (dictWard[c.id] = c.name))
    }

    for (let item of res[0]) {
      item.cityName = dictCity[item.cityId]
      item.districtName = dictDistrict[item.districtId]
      item.wardName = dictWard[item.wardId]
      item.quantity = await item.__products__.filter((x) => x.isDeleted === false).reduce((sum, current) => sum + Number(current.quantity), 0)
      // const lstDonDangSoan: any = await authApiHelper.findListOrder(req, { warehouseId: item.id, status: enumData.OrderStatus.DonDangSoan.code })
      // const lstDonLenDon: any = await authApiHelper.findListOrder(req, { warehouseId: item.id, isDaLenDon: true })

      // for (let dt of await item.__products__) {
      //   if (dt.warehouseId === item.id) {
      //     const quantityTotalDangSoan = lstDonDangSoan
      //       .filter((x: any) => x.productId === dt.productId)
      //       .reduce((sum, current) => sum + Number(current.quantity), 0)
      //     dt.quantityDangSoan = quantityTotalDangSoan || 0

      //     const quantityDaLenDon = lstDonLenDon
      //       .filter((x: any) => x.productId === dt.productId)
      //       .reduce((sum, current) => sum + Number(current.quantity), 0)
      //     dt.quantityDaLenDon = quantityDaLenDon || 0
      //   }
      // }
      // item.quantityDangSoan = await item.__products__.reduce((sum, current) => sum + Number(current.quantityDangSoan), 0)
      // item.quantityDaLenDon = await item.__products__.reduce((sum, current) => sum + Number(current.quantityDaLenDon), 0)
      delete item.__products__
    }

    return res
  }

  async paginationMBCInventory(data: PaginationDto, req: IRequest) {
    let whereCon: any = { type: NSWarehouse.EWarehouseType.MBC }
    if (data.where.productId) {
      const lstDetail = await this.warehouseProductDetailRepo.find({ where: { productId: data.where.productId } })
      if (lstDetail.length === 0) return [[], 0]
      const lstdId = lstDetail.map((x) => x.warehouseId)
      whereCon.id = In(lstdId)
    }

    if (data.where.name) whereCon.name = ILike(`%${data.where.name}%`)
    if (data.where.cityId) whereCon.cityId = data.where.cityId
    if (data.where.districtId) whereCon.districtId = data.where.districtId
    if (data.where.wardId) whereCon.wardId = data.where.wardId
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.warehouseId) whereCon.id = data.where.warehouseId
    if (data.where.storeId) {
      const warehouse = await this.warehouseRepository.find({ where: { storeId: data.where.storeId } })
      if (warehouse.length > 0) {
        whereCon.id = In(warehouse.map((item) => item.id))
      }
    }
    whereCon.products = {}
    // whereCon.products.product = {}
    if (data.where.brandId) {
      whereCon.products.product.brandId = Like(`%${data.where.brandId}%`)
    }

    let res: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
      relations: { products: { details: true } },
    })
    if (res[0].length === 0) return []

    for (let item of res[0]) {
      item.quantity = await item.__products__.filter((x) => x.isDeleted === false).reduce((sum, current) => sum + Number(current.quantity), 0)
      delete item.__products__
    }

    return res
  }

  async updateQuantityReturn(data: { lstDetail: any[] }, req: IRequest, user?: UserDto) {
    return await this.repo.manager.transaction('SERIALIZABLE', async (trans) => {
      const repo = trans.getRepository(WarehouseProductEntity)
      const detailRepo = trans.getRepository(WarehouseProductDetailEntity)

      for (const detail of data.lstDetail) {
        const whProduct = await repo.findOne({
          where: { productId: detail.productId, warehouseId: detail.warehouseId, isDeleted: false },
        })
        const newQuantity = Number(whProduct.quantity) + Number(detail.quantityReturn)
        await repo.update(whProduct.id, { quantity: newQuantity, updatedAt: new Date(), updatedBy: user?.id })
        const pdDetail = await detailRepo.findOne({
          where: {
            productDetailId: detail.productDetailId,
            isDeleted: false,
            warehouseProductId: whProduct.id,
            warehouseId: detail.warehouseId,
            productId: detail.productId,
          },
        })
        if (!pdDetail)
          throw new Error(
            `Sản phẩm ${detail.productName} có hạn sử dụng ${moment(detail.expiryDate).format(
              'YYYY-MM-DD',
            )} không còn tồn tại.Vui lòng kiểm tra lại `,
          )

        const quantityDetailNew = Number(pdDetail.quantity) + Number(detail.quantityReturn)
        pdDetail.quantity = quantityDetailNew
        await detailRepo.update(pdDetail.id, { quantity: quantityDetailNew })
      }
      return { message: UPDATE_SUCCESS }
    })
  }

  /** Import số lượng tồn đầu kỳ cho kho */
  async importQuantityBegin(data: InboundCreateDto[], user?: UserDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(WarehouseProductEntity)
      const warehouseItemRepo = trans.getRepository(WarehouseProductEntity)
      const warehouseItemDetailRepo = trans.getRepository(WarehouseProductDetailEntity)
      const itemRepo = trans.getRepository(ItemEntity)
      const itemDetailRepo = trans.getRepository(ItemDetailEntity)

      data = data.map((item) => {
        item.itemName = (item.itemName || '').trim()
        return item
      })

      // Lấy ds sp theo tên
      const lstItemName = data.mapAndDistinct((item) => item.itemName)
      const lstItem: any[] = await itemRepo.find({
        where: { name: In(lstItemName) },
        relations: { details: true },
      })
      const mapItem = lstItem.convertToMap((item) => item.name)

      // lấy ds kho theo mã
      const lstWarehouseCode = data.mapAndDistinct((item) => item.warehouseCode)
      const lstWarehouse = await this.warehouseRepository.find({
        where: { code: In(lstWarehouseCode) },
        select: { id: true, code: true, name: true },
      })
      const lstWhId = lstWarehouse.map((item) => item.id)
      const mapWarehouse = lstWarehouse.convertToMap((item) => item.code)

      // check sp, kho
      for (const item of data) {
        const curItem = mapItem.get(item.itemName)
        if (!curItem) throw new Error(`Không tìm thấy tên sản phẩm [${item.itemName}]`)

        const curWh = mapWarehouse.get(item.warehouseCode)
        if (!curWh) throw new Error(`Không tìm thấy kho [${item.warehouseCode}]`)
      }

      // ds chi tiết sp kho
      const lstWhDetail: any[] = await repo.find({ where: { warehouseId: In(lstWhId) }, relations: { details: true } })

      for (const item of data) {
        const curItem = mapItem.get(item.itemName)
        const curWh = mapWarehouse.get(item.warehouseCode)

        const manufactureDateFmt = item.manufactureDate ? moment(item.manufactureDate).format('DD-MM-YYYY') : ''
        const expiryDateFmt = item.expiryDate ? moment(item.expiryDate).format('DD-MM-YYYY') : ''

        const keyItem = `${curItem.id}/${curWh.id}`
        const keyItemDetail = `${manufactureDateFmt}/${expiryDateFmt}/${item.lotNumber}`

        // thêm detail cho sp
        const lstItemDetail = curItem.__details__
        let foundItemDetail = lstItemDetail.find((d) => {
          const manufactureDateFmt = d.manufactureDate ? moment(d.manufactureDate).format('DD-MM-YYYY') : ''
          const expiryDateFmt = d.expiryDate ? moment(d.expiryDate).format('DD-MM-YYYY') : ''
          const key = `${manufactureDateFmt}/${expiryDateFmt}/${d.lotNumber}`
          return key === keyItemDetail
        })

        let quantityBeginBefore = coreHelper.selectSum(lstItemDetail, 'quantityBegin')
        if (!foundItemDetail) {
          const newItemDetail = new ItemDetailEntity()
          newItemDetail.itemId = curItem.id
          newItemDetail.quantity = +item.quantityBegin || 0
          newItemDetail.quantityBegin = +item.quantityBegin || 0
          newItemDetail.manufactureDate = item.manufactureDate
          newItemDetail.expiryDate = item.expiryDate
          newItemDetail.lotNumber = item.lotNumber
          newItemDetail.createdAt = new Date()
          newItemDetail.createdBy = user?.id
          newItemDetail.id = uuidv4()
          await itemDetailRepo.insert(newItemDetail)
          quantityBeginBefore += newItemDetail.quantityBegin
          foundItemDetail = { id: newItemDetail.id }
        } else {
          await itemDetailRepo.update(foundItemDetail.id, {
            quantity: foundItemDetail.quantity - foundItemDetail.quantityBegin + (+item.quantityBegin || 0),
            quantityBegin: item.quantityBegin || 0,
            updatedAt: new Date(),
            updatedBy: user?.id,
          })
          quantityBeginBefore = quantityBeginBefore - foundItemDetail.quantityBegin + item.quantityBegin
        }
        await itemRepo.update(curItem.id, { quantityBegin: quantityBeginBefore })

        // thêm sp, detail sp cho kho
        const foundWhItem = lstWhDetail.find((d) => `${d.productId}/${d.warehouseId}` === keyItem)
        if (!foundWhItem) {
          const newWhItem = new WarehouseProductEntity()
          newWhItem.warehouseId = curWh.id
          newWhItem.productId = curItem.id
          newWhItem.quantity = +item.quantityBegin || 0
          newWhItem.quantityBegin = +item.quantityBegin || 0
          newWhItem.createdAt = new Date()
          newWhItem.createdBy = user?.id
          newWhItem.id = uuidv4()
          await warehouseItemRepo.insert(newWhItem)

          const newWhItemDetail = new WarehouseProductDetailEntity()
          newWhItemDetail.warehouseId = curWh.id
          newWhItemDetail.productId = curItem.id
          newWhItemDetail.warehouseProductId = newWhItem.id
          newWhItemDetail.quantity = +item.quantityBegin || 0
          newWhItemDetail.quantityBegin = +item.quantityBegin || 0
          newWhItemDetail.manufactureDate = item.manufactureDate
          newWhItemDetail.expiryDate = item.expiryDate
          newWhItemDetail.lotNumber = item.lotNumber
          newWhItemDetail.createdAt = new Date()
          newWhItemDetail.createdBy = user?.id
          newWhItemDetail.id = uuidv4()
          await warehouseItemDetailRepo.insert(newWhItemDetail)
        } else {
          await warehouseItemRepo.update(foundWhItem.id, {
            quantity: foundWhItem.quantity - foundWhItem.quantityBegin + (+item.quantityBegin || 0),
            quantityBegin: item.quantityBegin || 0,
            updatedAt: new Date(),
            updatedBy: user?.id,
          })

          const lstWhItemDetail = foundWhItem.__details__ || []
          let quantityBeginWhBefore = coreHelper.selectSum(lstWhItemDetail, 'quantityBegin')
          const foundWhItemDetail = lstWhItemDetail.find((d) => {
            const manufactureDateFmt = d.manufactureDate ? moment(d.manufactureDate).format('DD-MM-YYYY') : ''
            const expiryDateFmt = d.expiryDate ? moment(d.expiryDate).format('DD-MM-YYYY') : ''
            const key = `${manufactureDateFmt}/${expiryDateFmt}/${d.lotNumber}`
            return key === keyItemDetail
          })
          if (!foundWhItemDetail) {
            const newWhItemDetail = new WarehouseProductDetailEntity()
            newWhItemDetail.warehouseId = curWh.id
            newWhItemDetail.productId = curItem.id
            newWhItemDetail.warehouseProductId = foundWhItem.id
            newWhItemDetail.productDetailId = foundItemDetail.id
            newWhItemDetail.quantity = +item.quantityBegin || 0
            newWhItemDetail.quantityBegin = +item.quantityBegin || 0
            newWhItemDetail.manufactureDate = item.manufactureDate
            newWhItemDetail.expiryDate = item.expiryDate
            newWhItemDetail.lotNumber = item.lotNumber
            newWhItemDetail.createdAt = new Date()
            newWhItemDetail.createdBy = user?.id
            newWhItemDetail.id = uuidv4()
            await warehouseItemDetailRepo.insert(newWhItemDetail)
            quantityBeginWhBefore += newWhItemDetail.quantityBegin
          } else {
            await warehouseItemDetailRepo.update(foundWhItemDetail.id, {
              quantity: foundWhItemDetail.quantity - foundWhItemDetail.quantityBegin + (+item.quantityBegin || 0),
              quantityBegin: item.quantityBegin || 0,
              updatedAt: new Date(),
              updatedBy: user?.id,
            })
            quantityBeginWhBefore = quantityBeginWhBefore - foundWhItemDetail.quantityBegin + item.quantityBegin
          }
          await itemRepo.update(foundWhItem.id, { quantityBegin: quantityBeginBefore })
        }
      }
    })
    return { message: UPDATE_SUCCESS }
  }

  /** Tạo định mức tồn kho an toàn */
  async createWarehouseProductSafety(data: WarehouseProductSafetyCreateDto, user?: UserDto) {
    const foundWh = await this.repo.findOne({ where: { id: data.warehouseId, isDeleted: false } })
    if (!foundWh) throw new Error(ERROR_NOT_FOUND_DATA + ' [ Kho ] ')

    const foundItem = await this.itemRepo.findOne({ where: { id: data.productId, isDeleted: false } })
    if (!foundItem) throw new Error(ERROR_NOT_FOUND_DATA + ' [ Sản phẩm ] ')

    const checkWhItem = await this.warehouseProductSafetyRepo.findOne({
      where: { warehouseId: data.warehouseId, productId: data.productId, isDeleted: false },
    })
    if (!checkWhItem) {
      const newWhItem = new WarehouseProductSafetyEntity()
      newWhItem.warehouseId = data.warehouseId
      newWhItem.productId = data.productId
      newWhItem.productCode = foundItem.code
      newWhItem.productName = foundItem.name
      newWhItem.quantityMin = +data.quantityMin || 0
      newWhItem.quantityMax = +data.quantityMax || 0
      newWhItem.createdAt = new Date()
      newWhItem.createdBy = user?.id
      newWhItem.id = uuidv4()
      await this.warehouseProductSafetyRepo.insert(newWhItem)
    } else {
      await this.warehouseProductSafetyRepo.update(checkWhItem.id, {
        quantityMin: +data.quantityMin || 0,
        quantityMax: +data.quantityMax || 0,
        updatedAt: new Date(),
        updatedBy: user?.id,
      })
    }
    return { message: UPDATE_SUCCESS }
  }

  /** Import định mức tồn kho an toàn */
  async importWarehouseProductSafety(data: WarehouseProductSafetyImportDto[], user?: UserDto) {
    const lstWhCode = data.mapAndDistinct((d) => d.warehouseCode)
    const lstWh = await this.warehouseRepository.find({ where: { code: In(lstWhCode), isDeleted: false } })
    const mapWh = lstWh.convertToMap((wh) => wh.code)

    const lstProductName = data.mapAndDistinct((d) => d.productName)
    const lstItem = await this.itemRepo.find({ where: { name: In(lstProductName), isDeleted: false } })
    const mapItem = lstItem.convertToMap((item) => item.name)

    for (const item of data) {
      const foundWh = mapWh.get(item.warehouseCode)
      if (!foundWh) throw new Error(ERROR_NOT_FOUND_DATA + ` [ Kho ] [${item.warehouseCode}] `)

      const foundItem = mapItem.get(item.productName)
      if (!foundItem) throw new Error(ERROR_NOT_FOUND_DATA + ` [ Sản phẩm ] [${item.productName}]`)
    }

    const lstWhItemSafety = await this.warehouseProductSafetyRepo.find({ where: { warehouseId: In(lstWh.map((wh) => wh.id)), isDeleted: false } })

    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(WarehouseProductSafetyEntity)
      const lstInsert = []
      const lstUpdate = []
      for (let item of data) {
        const foundWh = mapWh.get(item.warehouseCode)
        const foundItem = mapItem.get(item.productName)
        const checkWhItem = lstWhItemSafety.find((whItem) => whItem.warehouseId === foundWh.id && whItem.productId === foundItem.id)
        if (!checkWhItem) {
          const newWhItem = new WarehouseProductSafetyEntity()
          newWhItem.warehouseId = foundWh.id
          newWhItem.productId = foundItem.id
          newWhItem.productCode = foundItem.code
          newWhItem.productName = foundItem.name
          newWhItem.quantityMin = +item.quantityMin || 0
          newWhItem.quantityMax = +item.quantityMax || 0
          newWhItem.createdAt = new Date()
          newWhItem.createdBy = user?.id
          newWhItem.id = uuidv4()
          lstInsert.push(newWhItem)
        } else {
          lstUpdate.push(
            repo.update(checkWhItem.id, {
              quantityMin: +item.quantityMin || 0,
              quantityMax: +item.quantityMax || 0,
              updatedAt: new Date(),
              updatedBy: user?.id,
            }),
          )
        }
      }
      await repo.insert(lstInsert)
      await Promise.all(lstUpdate)
    })
    return { message: UPDATE_SUCCESS }
  }

  /** Hàm tăng số lượng đã bán của sản phẩm khi khách hàng thanh toán */
  async increaseQuantityOrder(data: IncreaseQuantityOrderDto[], poType?: string) {
    try {
      const productIds = data.map((d) => d.productId)
      const lstProduct = await this.itemRepo.find({ where: { id: In(productIds) } })
      // Duyệt sanh sách sản phẩm
      for (const item of data) {
        // Lấy ra supplier của sản phẩm
        const supplier = lstProduct.find((p) => p.id === item.productId)?.supplierId

        const product = lstProduct.find((p) => p.id === item.productId)

        // Tìm kho của WH
        const wh = await this.warehouseRepository.findOne({ where: { storeId: supplier, isDeleted: false } })
        if (!wh) throw new Error(ERROR_NOT_FOUND_DATA + ` [ Kho ] [${supplier}]`)

        // Tìm sản phẩm trong kho
        const whItem = await this.warehouseProductRepo.findOne({ where: { warehouseId: wh.id, productId: item.productId } })

        if (!whItem) {
          // ! Trường hợp không có hàng, vì combo không được nhập kho NCC
          // throw new Error(ERROR_NOT_FOUND_DATA + ` [ Sản phẩm ] [${item.productId}]`)
          if (product.isCombo) {
            const quantityCombo = item.quantity // Số lượng combo

            // Lấy sẩn phẩm trong combo
            const productInCombo = await this.itemComboRepo.find({ where: { itemId: item.productId } })

            // Nhân thành phần với số lượng combo
            const mapping = productInCombo.map((i) => {
              return {
                productId: i.itemInComboId,
                quantity: +quantityCombo * +i.quantity,
              }
            })

            const itemLst = await this.itemRepo.find({ where: { id: In(mapping.map((p) => p.productId)) } })
            const supplierIds = Array.from(new Set(itemLst.map((i) => i.supplierId)))
            const wh = await this.warehouseRepository.find({ where: { storeId: In(supplierIds), isDeleted: false } })

            const currentLst = await this.warehouseProductRepo.find({
              where: {
                warehouseId: wh[0].id,
                productId: In(mapping.map((p) => p.productId)),
              },
            })

            // Cộng số lượng đã bán sản phẩm trong combo
            for (const m of mapping) {
              const i = currentLst.find((c) => c.productId === m.productId) || { quantityOrder: 0 }
              await this.warehouseProductRepo.update(
                { warehouseId: wh[0].id, productId: m.productId },
                {
                  quantityOrder: +m.quantity + +i.quantityOrder,
                },
              )
            }
          }
        } else {
          // ! Trường hợp có hàng
          // Tăng số lượng đã bán của sản phẩm
          if (poType == NSPo.EPoType.MANUAL) {
            // Khi approve phiếu nhập kho thì quantity trong kho đã được tăng
            // await this.warehouseProductRepo.update(whItem.id, { quantity: whItem.quantity + item.quantity })
          } else {
            if (product.isCombo) {
              const quantityCombo = item.quantity // Số lượng combo

              // Lấy sẩn phẩm trong combo
              const productInCombo = await this.itemComboRepo.find({ where: { itemId: item.productId } })

              // Nhân thành phần với số lượng combo
              const mapping = productInCombo.map((i) => {
                return {
                  productId: i.itemInComboId,
                  quantity: +quantityCombo * +i.quantity,
                }
              })

              const itemLst = await this.itemRepo.find({ where: { id: In(mapping.map((p) => p.productId)) } })
              const supplierIds = Array.from(new Set(itemLst.map((i) => i.supplierId)))
              const wh = await this.warehouseRepository.find({ where: { storeId: In(supplierIds), isDeleted: false } })

              const currentLst = await this.warehouseProductRepo.find({
                where: {
                  warehouseId: wh[0].id,
                  productId: In(mapping.map((p) => p.productId)),
                },
              })

              // Cộng số lượng đã bán sản phẩm trong combo
              for (const m of mapping) {
                const i = currentLst.find((c) => c.productId === m.productId)
                await this.warehouseProductRepo.update(
                  { warehouseId: wh[0].id, productId: m.productId },
                  {
                    quantityOrder: +m.quantity + +i.quantityOrder,
                  },
                )
              }
            } else {
              // PO là WITHCOMBO (KỲ) và PO Retail (Lẻ)
              await this.warehouseProductRepo.update(
                { id: whItem.id },
                {
                  quantityOrder: whItem.quantityOrder + item.quantity,
                },
              )
            }
          }
        }
      }
      return { message: UPDATE_SUCCESS }
    } catch (error) {
      console.log(error)
      throw new Error()
    }
  }

  async decreaseQuantityOrder(data: IncreaseQuantityOrderDto[]) {
    const productIds = data.map((d) => d.productId)
    const lstProduct = await this.itemRepo.find({ where: { id: In(productIds), isDeleted: false } })
    for (const item of data) {
      const supplier = lstProduct.find((p) => p.id === item.productId)?.supplierId

      // Tìm kho của WH
      const wh = await this.warehouseRepository.findOne({ where: { storeId: supplier, isDeleted: false } })
      if (!wh) throw new Error(ERROR_NOT_FOUND_DATA + ` [ Kho ] [${supplier}]`)

      // Tìm sản phẩm trong kho
      const whItem = await this.warehouseProductRepo.findOne({ where: { warehouseId: wh.id, productId: item.productId, isDeleted: false } })
      if (!whItem) throw new Error(ERROR_NOT_FOUND_DATA + ` [ Sản phẩm ] [${item.productId}]`)

      // Trừ số lượng đã bán của sản phẩm
      await this.warehouseProductRepo.update(whItem.id, {
        quantity: whItem.quantity - item.quantity,
        quantityOrder: whItem.quantityOrder - item.quantity,
      })
    }
    return { message: UPDATE_SUCCESS }
  }
}
