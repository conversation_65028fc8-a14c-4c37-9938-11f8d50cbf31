import { Controller, UseGuards, Post, Body, Req } from '@nestjs/common'
import { OutboundService } from './outbound.service'
import { OutboundCreateComboDto, OutboundCreateDto, OutboundUpdateComboDto, OutboundUpdateDto } from './dto'
import { FilterOneDto, PaginationDto, UserDto } from '../../../dto'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Request as IRequest } from 'express'
import { JwtAuthGuard } from '../../common/guards'
import { CurrentUser } from '../../common/decorators'
import { CheckingRemainDto } from './dto/checkRemail.dto'
import { AuthGuard } from '../../common/guards/auth.guard'

@ApiBearerAuth()
@ApiTags('Outbound')
@UseGuards(AuthGuard)
@Controller('outbound_public')
export class OutboundPublicController {
  constructor(private readonly service: OutboundService) { }

  @ApiOperation({ summary: 'Tì<PERSON> kiếm' })
  @Post('find')
  async find(@Body() data: any) {
    return await this.service.find(data)
  }

  @ApiOperation({ summary: 'Tìm chi tiết' })
  @Post('find_detail')
  async findDetail(@Body() data: FilterOneDto, @Req() req: IRequest) {
    return await this.service.findDetail(data, req)
  }

  @ApiOperation({ summary: 'Hàm tự gen mã' })
  @Post('get_auto_gen_code')
  async getCodeAutoGen() {
    return await this.service.getCodeAutoGen()
  }

  @ApiOperation({ summary: 'Phân trang' })
  @Post('pagination')
  async pagination(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.pagination(data, req)
  }

  @ApiOperation({ summary: 'Phân trang lịch sử' })
  @Post('pagination_for_history_view')
  async paginationForHistoryView(@Body() data: PaginationDto, @Req() req: IRequest) {
    return await this.service.paginationForHistoryView(data, req)
  }

  @ApiOperation({ summary: 'xác nhận soạn hàng cho phiếu xuất kho có orderId' })
  @Post('update_confirm_preparation')
  async updateConfirmPreparation(@Body() data: FilterOneDto) {
    return await this.service.updateConfirmPreparation(data)
  }

  @Post('check_remain')
  async checkRemain(@Body() data: CheckingRemainDto[], @Req() req: IRequest) {
    return await this.service.checkRemain(data, req)
  }

  @ApiOperation({ summary: 'duyệt phiếu xuất kho' })
  @Post('update_approve')
  async updateApprove(@Body() data: FilterOneDto, @Req() req: IRequest) {
    return await this.service.updateApprove(data, req)
  }

  @ApiOperation({ summary: 'Hàm huỷ phiếu xuất kho' })
  @Post('update_cancel')
  async updateCancel(@Req() req: IRequest, @Body() data: FilterOneDto) {
    return await this.service.updateCancel(data, req)
  }

  @ApiOperation({ summary: 'àm tạo mới phiếu xuất kho' })
  @Post('create_data')
  async createData(@Body() data: OutboundCreateDto, @Req() req: IRequest) {
    return await this.service.createData(data, null, req)
  }

  @ApiOperation({ summary: 'Hàm chỉnh sửa phiếu xuất kho' })
  @Post('update_data')
  async updateData(@Body() data: OutboundUpdateDto, @Req() req: IRequest) {
    return await this.service.updateData(data, req)
  }

  @ApiOperation({ summary: ' Hàm tạo mới phiếu xuất kho tạo combo' })
  @Post('create_data_combo')
  async createDataCombo(@Body() data: OutboundCreateComboDto, @Req() req: IRequest) {
    return await this.service.createDataCombo(data, req)
  }

  @ApiOperation({ summary: 'Hàm chỉnh sửa phiếu xuất kho tạo combo' })
  @Post('update_data_combo')
  async updateDataCombo(@Body() data: OutboundUpdateComboDto, @Req() req: IRequest) {
    return await this.service.updateDataCombo(data, req)
  }

  @ApiOperation({ summary: 'Hàm chỉnh sửa phiếu xuất kho tạo combo' })
  @Post('update_approve_combo')
  async updateApproveCombo(@Body() data: FilterOneDto, @Req() req: IRequest) {
    return await this.service.updateApproveCombo(data, req)
  }
}
