import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { SupplierCreateDto } from './dto/supplierCreate.dto'
import { SupplierUpdateDto } from './dto/supplierUpdate.dto'
import { In, Like } from 'typeorm'
import { ActionLogService } from '../actionLog/actionLog.service'
import { SupplierCreateExcelDto, SupplierRegisterDto } from './dto'
import { PaginationDto, UserDto } from '../../../dto'
import {
  CREATE_SUCCESS,
  enumData,
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  IMPORT_SUCCESS,
  NSOperational,
  NSWarehouse,
  PWD_SALT_ROUNDS,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../../constants'
import { CityEntity, DistrictEntity, SupplierEntity, UserEntity, WardEntity } from '../../../entities'
import { coreHelper } from '../../../helpers'
import {
  CityRepository,
  DistrictRepository,
  OperationalAreaRepository,
  PartnerMapRepository,
  SupplierRepository,
  WarehouseRepository,
  UserRepository,
} from '../../../repositories'
import { hash } from 'bcrypt'
import { UserRegisterDto, UserUpdateDto, UserUpdatePasswordDto } from '../auth/dto/register.dto'
import { plainToClass, plainToInstance } from 'class-transformer'
@Injectable()
export class SupplierService {
  constructor(
    private readonly repo: SupplierRepository,
    private readonly userRepo: UserRepository,
    private readonly actionService: ActionLogService,
    private readonly partnerMapRepo: PartnerMapRepository,
    private readonly warehouseRepo: WarehouseRepository,
    private readonly cityRepo: CityRepository,
    private readonly districtRepo: DistrictRepository,
    private readonly operationalAreaRepo: OperationalAreaRepository,
  ) {}

  public async find(data: any) {
    return await this.repo.find(data)
  }

  public async createData(user: UserDto, data: SupplierCreateDto) {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
    if (checkCodeExist) throw new Error(ERROR_CODE_TAKEN)

    const newEntity = this.repo.create({
      ...data,
    })
    newEntity.createdBy = user?.id
    const createdEntity = await this.repo.save(newEntity)

    /** Kiểm tra nếu là is3PL HOẶC supplier thì sinh ra WH */
    let newWH: any
    if (data.is3PL) {
      newWH = await this.warehouseRepo.save({
        code: `3PL_${createdEntity.code}`,
        name: createdEntity.name,
        phone: createdEntity.phone,
        address: createdEntity.address,
        wardId: createdEntity.wardId,
        districtId: createdEntity.districtId,
        cityId: createdEntity.cityId,
        storeId: createdEntity.id,
        type: NSWarehouse.EWarehouseType['3PL'],
      })
    } else if (data.isSupplier) {
      newWH = await this.warehouseRepo.save({
        code: `SUPPLIER_${createdEntity.code}`,
        name: createdEntity.name,
        phone: createdEntity.phone,
        address: createdEntity.address,
        wardId: createdEntity.wardId,
        districtId: createdEntity.districtId,
        cityId: createdEntity.cityId,
        storeId: createdEntity.id,
        type: NSWarehouse.EWarehouseType.SUPPLIER,
      })
    }

    /** Tạo Vùng áp dụng TP/Tỉnh cho supplier */
    if (data.configProvince) {
      const city = await this.cityRepo.find()
      const lstCity = data.configProvince.map((item) => {
        const findCity = city.find((x) => x.id === item)
        return findCity
      })
      for (let item of lstCity) {
        await this.operationalAreaRepo.save({
          areaId: item.id,
          areaCode: item.code,
          areaName: item.name,
          type: NSOperational.EOperationalAreaType.PROVINCE,
          mappingRefType: NSOperational.EAppliedObjectType.SUPPLIER,
          mappingRefId: createdEntity.id,
          supplierId: createdEntity.id,
        })
      }
    }
    /** Tạo Vùng áp dụng Quận/Huyện cho supplier */
    if (data.configDistrict) {
      const district = await this.districtRepo.find()
      const lstDistrict = data.configDistrict.map((item) => {
        const findDistrict = district.find((x) => x.id === item)
        return findDistrict
      })
      for (let item of lstDistrict) {
        await this.operationalAreaRepo.save({
          areaId: item.id,
          areaCode: item.code,
          areaName: item.name,
          type: NSOperational.EOperationalAreaType.DISTRICT,
          mappingRefType: NSOperational.EAppliedObjectType.SUPPLIER,
          mappingRefId: createdEntity.id,
          supplierId: createdEntity.id,
        })
      }
    }

    return { message: CREATE_SUCCESS, warehouse: newWH }
  }

  public async updateData(user: UserDto, data: SupplierUpdateDto) {
    const entity = await this.repo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    if (data.code !== entity.code) {
      const checkCodeExist = await this.repo.findOne({ where: { code: data.code } })
      if (checkCodeExist) throw new Error(ERROR_CODE_TAKEN)
    }

    /** Cập nhật lại operational area */
    if (data.configProvince) {
      await this.operationalAreaRepo.delete({
        mappingRefId: data.id,
        mappingRefType: NSOperational.EAppliedObjectType.SUPPLIER,
        type: NSOperational.EOperationalAreaType.PROVINCE,
      })
      const city = await this.cityRepo.find()
      const lstCity = data.configProvince.map((item) => {
        const findCity = city.find((x) => x.id === item)
        return findCity
      })
      for (let item of lstCity) {
        await this.operationalAreaRepo.save({
          areaId: item.id,
          areaCode: item.code,
          areaName: item.name,
          mappingRefId: data.id,
          mappingRefType: NSOperational.EAppliedObjectType.SUPPLIER,
          type: NSOperational.EOperationalAreaType.PROVINCE,
        })
      }
    }
    /** Tạo Vùng áp dụng Quận/Huyện cho supplier */
    if (data.configDistrict) {
      await this.operationalAreaRepo.delete({
        mappingRefId: data.id,
        mappingRefType: NSOperational.EAppliedObjectType.SUPPLIER,
        type: NSOperational.EOperationalAreaType.DISTRICT,
      })
      const district = await this.districtRepo.find()
      const lstDistrict = data.configDistrict.map((item) => {
        const findDistrict = district.find((x) => x.id === item)
        return findDistrict
      })
      for (let item of lstDistrict) {
        await this.operationalAreaRepo.save({
          areaId: item.id,
          areaCode: item.code,
          areaName: item.name,
          mappingRefId: data.id,
          mappingRefType: NSOperational.EAppliedObjectType.SUPPLIER,
          type: NSOperational.EOperationalAreaType.DISTRICT,
        })
      }
    }

    await this.repo.update(data.id, {
      updatedBy: user?.id,
      name: data.name,
      email: data.email,
      code: data.code,
      phone: data.phone,
      address: data.address,
      description: data.description,
      wardId: data.wardId,
      cityId: data.cityId,
      districtId: data.districtId,
      isDistributor: data.isDistributor ?? false,
      isSupplier: data.isSupplier ?? false,
      is3PL: data.is3PL ?? false,
      parentId: data.parentId,
    })
    const res = {
      type: enumData.Type.ChinhSua.code,
      functionType: enumData.ActionLog.Setting_Supplier.code,
      code: data.code,
    }

    return { message: UPDATE_SUCCESS }
  }

  public async pagination(data: PaginationDto) {
    let whereCon: any = {}
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.phone) whereCon.phone = Like(`%${data.where.phone}%`)
    if (data.where.cityId) whereCon.cityId = data.where.cityId
    if (data.where.districtId) whereCon.districtId = data.where.districtId
    if (data.where.wardId) whereCon.wardId = data.where.wardId
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    if (data.where.is3PL) whereCon.is3PL = data.where.is3PL
    if (data.where.isSupplier) whereCon.isSupplier = data.where.isSupplier
    if (data.where.isDistributor) whereCon.isDistributor = data.where.isDistributor

    let res: any = await this.repo.findAndCount({
      relations: { ward: true, city: true, district: true },
      where: whereCon,
      order: { createdAt: 'DESC' },
      skip: data.skip,
      take: data.take,
    })
    for (let item of res[0]) {
      let cityObj = await item.__city__
      let districtObj = await item.__district__
      let wardObj = await item.__ward__
      item.cityName = cityObj?.name
      item.districtName = districtObj?.name
      item.wardName = wardObj?.name
      delete item.__city__
      delete item.__district__
      delete item.__ward__
      delete item.password
    }

    let listPartnerMap = await this.partnerMapRepo.find({ where: { parentId: In(res[0].map((x) => x.id)) } })
    const partnerMapCounts = listPartnerMap.reduce((acc, partner) => {
      acc[partner.parentId] = (acc[partner.parentId] || 0) + 1
      return acc
    }, {})

    for (let item of res[0] as any) {
      item.count = partnerMapCounts[item.id] || 0
      if (item.is3PL && item.isSupplier && item.isDistributor) item.count += 1
    }

    return { data: res[0], total: res[1] }
  }

  public async updateIsDelete(id: string, user: UserDto) {
    const entity = await this.repo.findOne({ where: { id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    entity.isDeleted = !entity.isDeleted
    await this.repo.update(entity.id, { isDeleted: entity.isDeleted })

    const type = enumData.Type.CapNhat.code
    const functionType = enumData.ActionLog.Setting_Supplier.code
    const res = { type: type, functionType: functionType, code: entity.code }
    // await this.actionService.createdata(res, user)

    return { message: UPDATE_ACTIVE_SUCCESS, entity }
  }

  public async findDetail(body: any) {
    const data = await this.repo.findOne({ where: { id: body.id } })
    delete data.password

    // Lấy ra thêm operational Area của 1 supplier
    const lstProvinceOperationalArea = await this.operationalAreaRepo.find({
      where: {
        mappingRefId: data.id,
        mappingRefType: NSOperational.EAppliedObjectType.SUPPLIER,
        type: NSOperational.EOperationalAreaType.PROVINCE,
      },
      select: ['areaId'],
    })
    data['configProvince'] = lstProvinceOperationalArea.map((item) => item.areaId)

    const districts = await this.districtRepo.find()

    const lstDistrictOperationalArea = await this.operationalAreaRepo.find({
      where: {
        mappingRefId: data.id,
        mappingRefType: NSOperational.EAppliedObjectType.SUPPLIER,
        type: NSOperational.EOperationalAreaType.DISTRICT,
      },
      select: ['areaId', 'areaName'],
    })
    data['configDistrict'] = lstDistrictOperationalArea.map((item) => ({
      id: item.areaId,
      cityId: districts.find((x) => x.id == item.areaId)?.cityId,
    }))

    return data
  }

  async createDataExcel(user: UserDto, data: SupplierCreateExcelDto[]) {
    await this.repo.manager.transaction(async (trans) => {
      const supplierRepo = trans.getRepository(SupplierEntity)
      const cityRepo = trans.getRepository(CityEntity)
      const districtRepo = trans.getRepository(DistrictEntity)
      const wardRepo = trans.getRepository(WardEntity)
      for (let item of data) {
        if (item.is3PL == null) item.is3PL = false
        if (item.isSupplier == null) item.isSupplier = false
        if (item.isDistributor == null) item.isDistributor = false
      }
      data = data.map((c) => ({
        ...c,
        cityCode: c.cityCode == '' ? null : c?.cityCode?.toString().padStart(2, '0'),
        districtCode: c.districtCode == '' ? null : c?.districtCode?.toString().padStart(3, '0'),
        wardCode: c.wardCode == '' ? null : c?.wardCode?.toString().padStart(5, '0'),
      }))
      const lstCityCode = coreHelper.selectDistinct(data, 'cityCode')
      const lstDistrictCode = coreHelper.selectDistinct(data, 'districtCode')
      const lstWardCode = coreHelper.selectDistinct(data, 'wardCode')

      const dicCode: any = {}
      {
        const listBrand: any[] = await supplierRepo.find({
          where: { isDeleted: false },
          select: { id: true, code: true },
        })
        listBrand.forEach((c) => (dicCode[c.code] = c))
      }

      const dicCity: any = {}
      {
        const lstCity: any = await cityRepo.find({ where: { code: In(lstCityCode), isDeleted: false } })
        lstCity.forEach((c) => (dicCity[c.code] = c))
      }

      const dicDistrict: any = {}
      {
        const lstDistrict: any = await districtRepo.find({ where: { code: In(lstDistrictCode), isDeleted: false } })
        lstDistrict.forEach((c) => (dicDistrict[c.code] = c))
      }

      const dicWard: any = {}
      {
        const lstWard: any = await wardRepo.find({ where: { code: In(lstWardCode), isDeleted: false } })
        lstWard.forEach((c) => (dicWard[c.code] = c))
      }
      let lstUsernameData = coreHelper.selectDistinct(data, 'username')
      const dicUsername: any = {}
      {
        const lstUsername: any = await this.repo.find({ where: { username: In(lstUsernameData) }, select: { username: true } })
        lstUsername.forEach((c) => (dicUsername[c.username] = c))
      }

      const lsBrandNew: SupplierEntity[] = []
      const dicCodeFile: any = {}
      for (const [idx, item] of data.entries()) {
        if (dicCodeFile[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Nhà Cung Cấp [${item.code}] trùng với dòng ${dicCodeFile[item.code]} ]`)
        if (dicCode[item.code]) throw new Error(`[ Dòng ${idx + 1} - Mã Nhà Cung Cấp [${item.code}] đã được sử dụng ]`)
        if (dicUsername[item.username]) throw new Error(`[ Dòng ${idx + 1} - Tên tài khoản [${item.code}] đã được sử dụng ]`)
        let cityId = null
        let districtId = null
        let wardId = null
        if (item.cityCode) {
          const city = dicCity[item.cityCode]
          if (!city) throw new Error(`[ Dòng ${idx + 3} - Mã Tỉnh/Thành phố [${item.cityCode}] không tồn tại ]`)
          cityId = city.id
          if (item.districtCode) {
            const district = dicDistrict[item.districtCode]
            if (!district) throw new Error(`[ Dòng ${idx + 3} - Mã Quận/Huyện [${item.districtCode}] không tồn tại ]`)
            if (district.cityId != cityId) {
              throw new Error(`[ Dòng ${idx + 3} - Mã Quận/Huyện [${item.districtCode}] không tồn tại trong Tỉnh/Thành phố [${item.cityCode}] ]`)
            }
            districtId = district.id
            if (item.wardCode) {
              const ward = dicWard[item.wardCode]
              if (!ward) throw new Error(`[ Dòng ${idx + 3} - Mã Phường/Xã [${item.wardCode}] không tồn tại ]`)
              if (ward.districtId != districtId) {
                throw new Error(`[ Dòng ${idx + 3} - Mã Phường/Xã [${item.wardCode}] không tồn tại trong Quận/Huyện [${item.districtCode}] ]`)
              }
              wardId = ward.id
            }
          }
        }
        const newLocation = supplierRepo.create({
          ...item,
          cityId: cityId,
          districtId: districtId,
          wardId: wardId,
          createdAt: new Date(),
          createdBy: user?.id,
        })
        lsBrandNew.push(newLocation)
        dicCodeFile[item.code] = idx + 1

        const type = enumData.Type.ThemMoi.code
        const functionType = enumData.ActionLog.Setting_Supplier.code
        const res = { type: type, functionType: functionType, code: item.code }
        // await this.actionService.createdata(res, user)
      }
      await supplierRepo.insert(lsBrandNew)
    })

    return { message: IMPORT_SUCCESS }
  }

  public async resetPassword(supplierId: string, password: string) {
    const entity = await this.repo.findOne({ where: { id: supplierId } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const newPassword = password
    const hashedPassword = await hash(newPassword, PWD_SALT_ROUNDS)
    await this.repo.update({ id: supplierId }, { password: hashedPassword })
    return { message: 'Password reset successfully', entity }
  }

  async supplierRegistration(req: Request, data: SupplierRegisterDto) {
    try {
      if (data.password !== data.confirmPassword) throw new Error('Mật khẩu không trùng khớp.')

      const result = await this.repo.manager.transaction(async (manager) => {
        const inforBank = JSON.stringify(data.inforBank)
        delete data.inforBank

        const userRepo = manager.getRepository(UserEntity)
        const supplierRepo = manager.getRepository(SupplierEntity)

        const checkUser = await userRepo.findOne({ where: { username: data.username }, select: { id: true } })
        if (checkUser) throw new Error('Tài khoản đã tồn tại.')

        // Tạo NCC
        const newEntity = supplierRepo.create({ ...data, infoA: inforBank })
        newEntity.status = enumData.SupplierStatus.MoiDangKy.code
        const supplierEntity = await supplierRepo.save(newEntity)
        return newEntity
      })

      return result
    } catch (error) {
      console.error('Lỗi khi đăng ký nhà cung cấp:', error)
      throw error
    }
  }

  async getSupplierInfo(user: UserDto) {
    const res: any = await this.repo.findOne({
      where: { id: user.id },
    })
    if (!res) throw new Error('Nhà cung cấp không tồn tại.')
    return res
  }

  public async registerSupplier(user: UserDto, data: UserRegisterDto) {
    // check username tồn tại hay chưa
    const objCheckUsername = await this.userRepo.findOne({
      where: { username: data.username },
      select: { id: true },
    })
    if (objCheckUsername) throw new NotFoundException(`Tài khoản đã tồn tại. Vui lòng thử lại với tên khác!`)

    const newUserEntity = this.userRepo.create({
      ...data,
      type: enumData.UserType.Supplier.code,
      createdBy: data.username,
      createdAt: new Date(),
    })
    const userEntity = await this.userRepo.save(newUserEntity)
    return { id: userEntity.id }
  }

  public async updateSupplier(data: UserUpdateDto) {
    data = plainToInstance(UserUpdateDto, data, { excludeExtraneousValues: true })
    const userEntity = await this.userRepo.findOne({ where: { id: data.id, type: enumData.UserType.Supplier.code }, select: { id: true } })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')
    console.log(data)
    await this.userRepo.update(userEntity.id, { ...data, updatedAt: new Date() })

    return { message: 'Cập nhật tài khoản thành công.' }
  }

  // updatePasswordSupplier
  public async updatePasswordSupplier(data: UserUpdatePasswordDto) {
    data = plainToInstance(UserUpdatePasswordDto, data, { excludeExtraneousValues: true })
    const userEntity = await this.userRepo.findOne({
      where: { id: data.id, type: enumData.UserType.Supplier.code },
      select: { id: true, password: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')
    //so sánh mật khẩu hiện tại
    let isMatch: boolean = await userEntity.comparePassword(data.currentPassword)
    if (!isMatch) throw new BadRequestException('Sai mật khẩu hiện tại.')

    if (data.newPassword === data.currentPassword) throw new BadRequestException('Trùng mật khẩu cũ.')

    const isCurrentPasswordMatch = await userEntity.comparePassword(data.currentPassword)
    if (!isCurrentPasswordMatch) throw new BadRequestException('Sai mật khẩu cũ.')

    const hashedPassword = await hash(data.newPassword, PWD_SALT_ROUNDS)
    await this.userRepo.update(userEntity.id, { password: hashedPassword, updatedAt: new Date() })

    return { message: 'Đổi mật khẩu thành công.' }
  }
}
